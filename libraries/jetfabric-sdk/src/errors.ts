import { ERROR_CODES, V2Error } from '@om/jetfabric-schemas';

export class JFError extends <PERSON><PERSON>r {
  readonly name: string;

  constructor(message: string) {
    super(message);

    Object.defineProperty(this, 'name', { value: new.target.name });
  }
}

export class JFServerError extends JF<PERSON>rror {
  readonly code: ERROR_CODES;

  constructor({ message, stack, code }: V2Error) {
    super(message);

    this.code = code || ERROR_CODES.UNKNOWN;
    this.stack = stack;
  }
}
