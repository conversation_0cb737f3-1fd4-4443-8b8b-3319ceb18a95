const DEVICE_MOBILE = 'mobile';
const DEVICE_DESKTOP = 'desktop';
const DEVICES = [DEVICE_MOBILE, DEVICE_DESKTOP];

const opposites = {
  top: 'bottom',
  bottom: 'top',
};

const marginMigration = (divider, sibling, side) => {
  if (!sibling) return;
  DEVICES.forEach((device) => {
    if (sibling[device]?.margin?.[side]) {
      divider[device].margin[opposites[side]] =
        Number(divider?.[device]?.margin?.[opposites[side]] || 0) +
        Number(sibling[device].margin[side] || 0);
    }
  });
};

export default ({ element, template, isNew }) => {
  if (isNew) element.data.version = 2;

  if (element.data.version !== 2) {
    const index = template.elements?.findIndex?.((e) => e.uid === element.uid);

    if (index === undefined) return;

    marginMigration(element, template.elements?.[index - 1] ?? null, 'bottom');
    marginMigration(element, template.elements?.[index + 1] ?? null, 'top');

    element.data.version = 2;
  }
};
