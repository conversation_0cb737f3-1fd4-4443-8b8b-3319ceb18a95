import { initWithDefaults } from '../utils';
import settings from '../settings';

export default (value) => {
  const element = value || {};

  element.desktop = initWithDefaults(element.desktop, {
    hidden: false,
    height: 30,
    margin: settings.margin(element.desktop.margin, { allSides: false }),
    zIndex: null,
  });

  element.mobile = initWithDefaults(element.mobile, {
    hidden: false,
    height: null,
    margin: settings.nullMargin(element.mobile.margin, { allSides: false }),
  });

  element.data = initWithDefaults(element.data, {
    type: 'spacer',
    customClasses: '',
  });

  return element;
};
