module.exports = {
  verbose: true,
  testFailureExitCode: process.env.CI ? 0 : 1,
  reporters: [
    'default',
    ['jest-junit', { outputDirectory: './coverage' }],
    ['@om/jest-num-failed', { outputDirectory: './coverage' }],
  ],
  coverageDirectory: './coverage',
  testEnvironment: 'node',
  testPathIgnorePatterns: ['./node_modules/'],
  collectCoverageFrom: ['./**/*.js', '!**/node_modules/**', '!**/translations*'],
  coverageReporters: [
    'html',
    'text',
    'cobertura',
    'lcov',
    ['text-summary', { file: 'coverage-summary.txt' }],
  ],
};
