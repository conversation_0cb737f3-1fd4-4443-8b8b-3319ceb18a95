{"name": "@om/common", "version": "0.1.0", "description": "Common", "author": "<PERSON> <<EMAIL>>", "homepage": "https://gitlab.com/optimonk/optimonk", "license": "Optimonk", "main": "src/index.js", "repository": {"type": "git", "url": "**************:optimonk/optimonk.git"}, "scripts": {"test:dev": "yarn run test --watchAll", "test": "TZ=UTC NODE_ENV=test jest --runInBand --env node --detectOpenHandles --forceExit", "test:unit:ci": "yarn run test --ci --coverage", "lint": "eslint ."}, "devDependencies": {"@om/jest-num-failed": "workspace:^", "jest": "^28.1.1", "jest-junit": "^15.0.0"}, "private": true}