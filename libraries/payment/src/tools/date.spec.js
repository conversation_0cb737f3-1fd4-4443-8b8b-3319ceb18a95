const moment = require('moment');
const { isInDateRange } = require('./date');

describe('Date testing tools', () => {
  beforeAll(() => {
    jest.useFakeTimers();
    jest.setSystemTime(moment('2023-08-03').utc().startOf('day').toDate());
  });

  it('inclusivity', () => {
    const start = moment('2023-08-03T00:00:00.000Z').utc().toDate();
    const end = moment(start).utc().add(2, 'days').endOf('day').toDate();
    jest.setSystemTime(start);
    const exactlyOnStartDate = isInDateRange(start, end);
    expect(exactlyOnStartDate).toBe(true);

    jest.setSystemTime(
      moment(start).add(1, 'day').add(8, 'hours').add(14, 'seconds').utc().toDate(),
    );
    const inBetweenDate = isInDateRange(start, end);
    expect(inBetweenDate).toBe(true);

    jest.setSystemTime(end);
    const exactlyOnEndDate = isInDateRange(start, end);
    expect(exactlyOnEndDate).toBe(true);

    jest.setSystemTime(moment(start).subtract(1, 'second').utc().toDate());
    const dateIsBefore = isInDateRange(start, end);
    expect(dateIsBefore).toBe(false);

    jest.setSystemTime(moment(end).add(1, 'second').utc().toDate());
    const dateIsAfter = isInDateRange(start, end);
    expect(dateIsAfter).toBe(false);

    jest.setSystemTime(jest.getRealSystemTime());
  });
});
