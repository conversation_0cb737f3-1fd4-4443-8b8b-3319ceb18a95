import * as models from '../models';
import * as filters from './filters';
import { V2_QUERY_TYPES, V2QueryBase } from './base';
import { ConditionalExpression } from '../conditional-expressions';
import { Value } from './data-reference';
import { V2Error } from '../error';

export { V2_QUERY_TYPES } from './base';

export interface QueryMostPopularProducts
  extends V2QueryBase,
    // Add fields for product filters
    Partial<filters.ProductResponseFilters>,
    // Add category id to filter for that category
    Partial<models.ProductCategory> {
  type: V2_QUERY_TYPES.MOST_POPULAR_PRODUCTS;
}

export interface QueryPreviouslyViewedProducts
  extends V2QueryBase,
    // Add fields for product filters
    Partial<filters.ProductResponseFilters> {
  type: V2_QUERY_TYPES.PREVIOUSLY_VIEWED_PRODUCTS;
}

export interface QueryCategoryData
  extends V2QueryBase,
    // Handle is the category's handle
    Partial<models.ProductCategory> {
  type: V2_QUERY_TYPES.CATEGORY_NAME;
}

export interface QueryCustomerHasTag extends V2QueryBase {
  type: V2_QUERY_TYPES.CUSTOMER_HAS_TAG;
  in: string[];
}

export interface QueryGraphQL extends V2QueryBase {
  type: V2_QUERY_TYPES.GRAPHQL;
  gql: string;
  values?: { [key: string]: Value };
}

export interface QueryClientState extends V2QueryBase {
  type: V2_QUERY_TYPES.CLIENT_STATE;
}

export interface QueryEvaluateCondition extends V2QueryBase {
  type: V2_QUERY_TYPES.CONDITION_EVALUATION;
  // queries is an object mapping the query id (string) to a V2Query object
  expression: ConditionalExpression;
  // Don't implicitly case to boolean
  preserveValue?: boolean;
}

export interface QueryEvaluateTemplate extends V2QueryBase {
  type: V2_QUERY_TYPES.TEMPLATE_EVALUATION;
  // queries is an object mapping the query id (string) to a V2Query object
  // values maps value id to Value object
  values: { [key: string]: Value };
  templateSyntax?: string;
  template: string;
}

export interface QueryCurrentPageType extends V2QueryBase {
  type: V2_QUERY_TYPES.CURRENT_PAGE_TYPE;
}

export type V2Query =
  | QueryMostPopularProducts
  | QueryPreviouslyViewedProducts
  | QueryCategoryData
  | QueryCustomerHasTag
  | QueryEvaluateCondition
  | QueryEvaluateTemplate
  | QueryCurrentPageType
  | QueryGraphQL
  | QueryClientState;

export function isQuery(maybeQuery: any): maybeQuery is V2Query {
  return (
    typeof maybeQuery === 'object' && 'type' in maybeQuery && maybeQuery.type in V2_QUERY_TYPES
  );
}

export type QueryExecutionResult =
  | { result: any }
  | { error: V2Error | undefined }
  | { partialResult: V2Query };
