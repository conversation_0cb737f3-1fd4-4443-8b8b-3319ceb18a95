{"activeCampaign": {"authFields": ["apiUrl", "<PERSON><PERSON><PERSON><PERSON>"], "fields": [], "bindable": false, "listIdField": "listId", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "actOn": {"authFields": ["username", "password"], "fields": [], "bindable": false, "listIdField": "listId", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "bizzy": {"authFields": ["<PERSON><PERSON><PERSON><PERSON>"], "fields": [], "bindable": false, "isGlobalOnly": true, "deprecated": true, "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "sendGrid": {"authFields": ["<PERSON><PERSON><PERSON><PERSON>"], "fields": [], "bindable": true, "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "campaignMonitor": {"authFields": ["<PERSON><PERSON><PERSON><PERSON>"], "fields": [], "bindable": true, "listIdField": "listId", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "cleverReach": {"authFields": ["<PERSON><PERSON><PERSON><PERSON>"], "fields": [], "bindable": false, "listIdField": "listId", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "shopifyCustomer": {"authFields": [], "bindable": true, "shopifyOnly": [true], "tagSeparator": [","], "tagFields": ["tags"], "requiredFieldValidation": [["or", "email", "phone"]], "failedValidationMessage": ["at_least_one_field_binded"], "listIdField": null, "fields": [], "requiredVariantFieldTypes": ["email", "phoneNumber"]}, "getResponse": {"authFields": ["<PERSON><PERSON><PERSON><PERSON>"], "fields": [], "bindable": true, "listIdField": null, "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "convertKit": {"authFields": ["<PERSON><PERSON><PERSON><PERSON>"], "fields": [], "bindable": false, "listIdField": null, "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "highLevel": {"authFields": ["access_token", "refresh_token", "expires_at", "locationId"], "isOAuth": true, "bindable": true, "listIdField": null, "tagSeparator": [","], "tagFields": ["tags"], "requiredFieldValidation": [["or", "email", "phone"]], "requiredVariantFieldTypes": ["email", "phoneNumber"]}, "hubSpot": {"deprecated": true, "authFields": ["<PERSON><PERSON><PERSON><PERSON>"], "fields": [], "bindable": true, "listIdField": "listId", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "hubSpotV2": {"authFields": ["access_token", "refresh_token", "expires_at"], "isOAuth": true, "bindable": true, "listIdField": "listId", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "klaviyo": {"authFields": ["<PERSON><PERSON><PERSON><PERSON>", "publicApiKey"], "fields": [], "bindable": true, "listIdField": "listId", "requiredFieldValidation": [["or", "email", "phone_number", "phone", "$phone_number"]], "failedValidationMessage": "no_$_sign", "requiredVariantFieldTypes": ["email", "phoneNumber"]}, "mailChimp": {"authFields": ["<PERSON><PERSON><PERSON><PERSON>"], "fields": ["listId", "doubleOptin", "updateExisting", "hiddenListGroupId"], "bindable": true, "listIdField": "listId", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "maileon": {"authFields": ["<PERSON><PERSON><PERSON><PERSON>"], "fields": [], "bindable": true, "listIdField": null, "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "mailerLite": {"authFields": ["<PERSON><PERSON><PERSON><PERSON>"], "fields": [], "bindable": false, "listIdField": "groupId", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "mailigen": {"authFields": ["<PERSON><PERSON><PERSON><PERSON>"], "fields": [], "bindable": true, "listIdField": "listId", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "moosend": {"authFields": ["<PERSON><PERSON><PERSON><PERSON>"], "fields": [], "bindable": false, "listIdField": "mailingList", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "soundest": {"authFields": ["<PERSON><PERSON><PERSON><PERSON>"], "fields": [], "bindable": true, "listIdField": null, "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "dotmailer": {"authFields": ["username", "password"], "fields": [], "bindable": false, "listIdField": null, "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "emarsys": {"authFields": ["username", "secret"], "fields": [], "bindable": false, "listIdField": "listId", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "infusionSoft": {"authFields": ["applicationName", "<PERSON><PERSON><PERSON><PERSON>"], "fields": [], "bindable": true, "listIdField": null, "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "interspire": {"authFields": ["apiUrl", "username", "userToken"], "fields": [], "bindable": false, "listIdField": "contactListName", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "mailjet": {"authFields": ["<PERSON><PERSON><PERSON><PERSON>", "secret<PERSON>ey"], "fields": [], "bindable": false, "listIdField": "listId", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "mailUp": {"authFields": ["client_id", "client_secret", "refresh_token", "access_token"], "oAuthFields": ["client_id", "client_secret"], "isOAuth": true, "fields": [], "bindable": true, "listIdField": "listId", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "mailWizz": {"authFields": ["apiUrl", "public<PERSON>ey", "privateKey"], "fields": [], "bindable": true, "listIdField": "listId", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "marketo": {"authFields": ["host", "clientId", "clientSecret"], "fields": [], "bindable": false, "listIdField": "listId", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "ontraport": {"authFields": ["appId", "<PERSON><PERSON><PERSON><PERSON>"], "fields": [], "bindable": true, "listIdField": null, "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "robly": {"authFields": ["<PERSON><PERSON><PERSON><PERSON>", "apiId"], "fields": [], "bindable": false, "listIdField": "listId", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "salesAutopilot": {"authFields": ["username", "password"], "fields": [], "bindable": true, "listIdField": "listId", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "aWeber": {"authFields": ["accessToken", "accessSecret", "consumerKey", "consumerSecret"], "isOAuth": true, "oAuthParams": ["authorization_code"], "fields": [], "bindable": true, "listIdField": "listId", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "automizy": {"authFields": ["accessToken"], "fields": [], "bindable": false, "listIdField": "listId", "isOAuth": true, "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "hirlevelmanager": {"authFields": ["categoryId", "lastNameField"], "fields": [], "bindable": false, "isGlobalOnly": true, "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "IContact": {"authFields": ["accountId", "clientFolderId", "applicationId", "userName", "apiPassword"], "fields": [], "bindable": false, "listIdField": "listId", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "ladyBird": {"authFields": ["postUrl"], "fields": [], "bindable": false, "isGlobalOnly": true, "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "listamester": {"authFields": ["apiUserId", "apiPassword", "groupListId"], "fields": [], "bindable": false, "isGlobalOnly": true, "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"], "deprecated": true}, "mailEngine": {"authFields": ["clientId", "subscribeId"], "fields": [], "bindable": false, "listIdField": null, "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "miniCrm": {"authFields": ["systemId", "<PERSON><PERSON><PERSON><PERSON>"], "fields": [], "bindable": true, "listIdField": "moduleName", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "salesforce": {"authFields": ["accessToken", "refreshToken", "signature", "scope", "idToken", "instanceUrl", "id", "tokenType", "issuedAt"], "isOAuth": true, "fields": [], "bindable": true, "listIdField": "listType", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "selzy": {"authFields": ["<PERSON><PERSON><PERSON><PERSON>"], "fields": [], "bindable": true, "listIdField": "listId", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "shopRenter": {"authFields": ["shopUrl", "apiUrl", "username", "password", "shopUrl"], "fields": [], "bindable": true, "isGlobalOnly": false, "listIdField": null, "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "silihost": {"authFields": ["url", "account", "charset"], "fields": [], "bindable": false, "isGlobalOnly": true, "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "verticalResponse": {"authFields": ["accessToken"], "isOAuth": true, "fields": [], "bindable": false, "listIdField": "listId", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "webGalamb3": {"authFields": ["url"], "fields": [], "bindable": false, "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "webGalamb4Plus": {"authFields": ["url"], "fields": [], "bindable": true, "listIdField": null, "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "webhook": {"authFields": ["callbackUrl"], "fields": [], "bindable": true, "listIdField": null, "requiredFieldValidation": null, "requiredVariantFieldTypes": null}, "slack": {"authFields": ["callbackUrl"], "fields": [], "bindable": true, "listIdField": null, "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "copernica": {"authFields": ["accessToken"], "isOAuth": true, "fields": [], "bindable": true, "listIdField": "databaseId", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "conversio": {"authFields": ["<PERSON><PERSON><PERSON><PERSON>"], "fields": [], "bindable": true, "listIdField": "listId", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "smsBump": {"authFields": ["access_token"], "isOAuth": true, "bindable": true, "shopifyOnly": [true], "listIdField": null, "fields": [], "requiredFieldValidation": [["phone"]], "requiredVariantFieldTypes": ["phoneNumber"]}, "attentive": {"authFields": ["authorizationToken"], "fields": [], "bindable": true, "listIdField": null, "requiredFieldValidation": [["phone"]], "requiredVariantFieldTypes": ["phoneNumber"]}, "keap": {"authFields": ["access_token"], "isOAuth": true, "bindable": true, "listIdField": null, "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "attentiveV2": {"authFields": ["authorizationToken"], "fields": [], "bindable": true, "listIdField": null, "requiredFieldValidation": [["or", "email", "phone"]], "requiredVariantFieldTypes": ["email", "phoneNumber"]}, "acerCCDB": {"authFields": ["accessToken", "refreshToken"], "fields": [], "isOAuth": true, "bindable": true, "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"], "deprecated": true}, "acerCCDBV2": {"authFields": ["username", "password"], "fields": [], "isOAuth": true, "bindable": true, "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "postscriptLegacy": {"authFields": ["legacyApiKey"], "fields": [], "bindable": true, "listIdField": null, "requiredFieldValidation": [["phone"]], "requiredVariantFieldTypes": ["phoneNumber"], "deprecated": true}, "postscript": {"authFields": ["<PERSON><PERSON><PERSON><PERSON>"], "fields": [], "bindable": true, "listIdField": null, "requiredFieldValidation": [["phone"]], "requiredVariantFieldTypes": ["phoneNumber"]}, "sendinblue": {"authFields": ["<PERSON><PERSON><PERSON><PERSON>"], "fields": [], "bindable": true, "listIdField": "listId", "requiredFieldValidation": [["or", "EMAIL", "SMS"]], "requiredVariantFieldTypes": ["email", "phoneNumber"]}, "listamesterV2": {"authFields": ["<PERSON><PERSON><PERSON><PERSON>", "userId"], "fields": [], "bindable": true, "listIdField": "listId", "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "recart": {"authFields": ["<PERSON><PERSON><PERSON><PERSON>"], "fields": [], "bindable": true, "shopifyOnly": [true], "requiredFieldValidation": [["phoneNumber"]], "requiredVariantFieldTypes": ["phoneNumber"]}, "unas": {"authFields": ["<PERSON><PERSON><PERSON><PERSON>"], "fields": [], "bindable": true, "requiredFieldValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "theMarketer": {"authFields": ["customerId", "restId"], "fields": [], "bindable": true, "requiredFeildValidation": [["email"]], "requiredVariantFieldTypes": ["email"]}, "zapier": {"authFields": ["callbackUrl"], "fields": [], "bindable": true, "listIdField": null, "requiredFieldValidation": null, "requiredVariantFieldTypes": null}}