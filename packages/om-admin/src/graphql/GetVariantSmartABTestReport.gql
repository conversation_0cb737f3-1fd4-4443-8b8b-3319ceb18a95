query getVariantSmartABTestReport($variantId: ID!) {
  getVariantSmartABTestReport: getVariantSmartABTestReport(variantId: $variantId) {
    _id
    uplift
    upliftId
    updatedAt
    changes {
      id
      index
      testsCount
      current {
        ...TestCaseFields
      }
      history {
        ...TestCaseFields
      }
    }
  }
}

fragment TestCaseFields on TestCase {
  id
  finishReason
  finishedAt
  startedAt
  control {
    ...ABTestItemFields
  }
  challenger {
    ...ABTestItemFields
  }
  evaluationCount
}

fragment ABTestItemFields on ABTestItem {
  id
  name
  text
  visitorsCount
  conversionCount
  chanceToWin
  uplift
  winner
}
