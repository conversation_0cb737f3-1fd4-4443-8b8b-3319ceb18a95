/* om hover, bar */

@mixin rowHover
  outline: 1px solid $om-orange

@mixin colHover
  background: rgba($om-orange, 0.2)

@mixin elementHover
  outline: 1px $om-orange solid

@mixin elementActive($opacity: 1)
  outline: 1px solid rgba($om-orange, $opacity)

@mixin colActive
  outline: 1px dashed $om-orange

@mixin rowActive
  outline: 1px solid $om-orange
  outline-offset: 1px

@mixin editModeTabBefore
  position: absolute
  font-weight: bold
  background-color: $om-orange
  font-family: $default-font !important
  color: #ffffff
  font-size: .75rem !important
  border-radius: 4px 4px 0 0
  left: -4px
  padding: 5px 8px
  justify-content: center
  width: 80px
  z-index: 10
  line-height: normal
  height: 24px
@mixin editModeTabAfter
  width: 100%
  height: 100%
  content: ''
  display: flex
  top: 4px
  left: 0
  position: absolute
  z-index: -1
  width: 80px

@mixin hoverBar($color)
  position: absolute
  color: white
  padding: 4px
  justify-content: center
  width: auto
  background-color: $color
  z-index: 10
  &:after
    width: 100%
    height: 100%
    content: ''
    display: flex
    bottom: -5px
    position: absolute
    z-index: -1

@mixin absolutePosition
  width: 100%
  height: 100%
  position: absolute
  top: 0
  left: 0
  right: 0
  pointer-events: none
  padding: 0 !important
  flex-basis: 100% !important
  max-width: 100% !important

@mixin absolutePositionSelected
  outline-offset: 0px

$dropLocationBorder: 1px solid $om-orange
@mixin dropLocation
  opacity: 1
  top: -13px
  left: 0
  right: 0
  bottom: auto
  margin: auto
  font-family: $default-font !important
  position: absolute
  font-size: 1rem !important
  width: 100%
  display: flex
  align-items: center
  justify-content: center
  height: $dropHeightNew
  color: $om-orange
  background: repeating-linear-gradient(-45deg, rgba($om-orange,0.15), rgba($om-orange,0.15) 10px, rgba($om-orange,0.3) 10px, rgba($om-orange,0.3) 20px)
  border: $dropLocationBorder
  text-align: center !important

.dropzone-v2
  display: none
  position: absolute
  top: 0
  left: 0
  right: 0
  z-index: 10
  height: 100%
  width: 100%
  &.debug
    background: rgba(0, 0, 255, 0.3)
    &.dropzone-v2-top
      background: rgba(255, 0, 0, 0.3)
    &.dropzone-v2-bottom
      background: rgba(0, 255, 0, 0.3)
    &.active
      background: rgba(0, 255, 255, 0.8)
      .indicator
        display: none
    &.disabled
      background: rgba(255, 255, 255, 0.7)
  &.disabled
    pointer-events: none

  &.dropzone-v2-top
    top: auto
    height: 50%
    bottom: 50%

    .indicator
      bottom: auto
  &.top-last
    height: 50%
  &.dropzone-v2-bottom
    top: 50%
    bottom: auto
    height: 50%

    .indicator
      top: auto
      bottom: 0.25rem

  &.dropzone-v2-left
    display: none
    top: auto
    left: 0
    right: auto
    bottom: auto
    width: 50%
    height: 2rem

    .indicator
      position: relative
      left: 0
      width: 0
      height: auto
      padding: 1rem 0
      margin: 0
      font-size: 15px !important
      line-height: 1
      @media screen and (max-width: 576px)
        font-size: 11px !important
      .element-dropzone-bar
        left: 0 !important
        bottom: 0 !important
        top: 100% !important
  &.left-last
    width: 50%

  &.dropzone-v2-right
    display: none
    top: auto
    left: auto
    right: 0
    bottom: auto
    width: 50% !important
    max-width: 100%
    height: 2rem

    .indicator
      position: relative
      right: 0
      width: 0
      height: auto
      padding: 1rem 0
      margin-left: auto
      margin-right: 0
      font-size: 15px !important
      line-height: 1
      @media screen and (max-width: 576px)
        font-size: 11px !important
      .element-dropzone-bar
        right: 0 !important
        bottom: 0 !important
        top: 100% !important
    &.active
      .indicator
        .element-dropzone-bar
          right: 0 !important
          left: auto !important

  &.dropzone-v2-full
    top: 0
    bottom: 0

  &.active
    .indicator
      display: flex
      .element-dropzone-bar
        display: block
        width: 116px
        height: 24px
        background: #ED5A29
        border-radius: 4px 4px 0px 0px
        position: absolute
        color: white
        left: 0
        margin-left: -1px
        bottom: 100%
        .title
          display: flex
          font-size: 12px !important
          text-align: center !important
          font-weight: bold
          font-family: 'Roboto', sans-serif !important
          line-height: 16px
          justify-content: center
          height: 100%
          align-items: center

  .indicator
    @include dropLocation
    top: 0
    display: none
    pointer-events: none
    z-index: 10000
    line-height: 1

.om-sidebar .dropzone-v2 .indicator
  font-size: 0.875rem !important
.row-dropzone
  @include dropLocation
  top: auto
  z-index: 10
  &:lang(hu)
    content: 'Dobd ide az elemet'

.edit-mode-tab
  display: none
.hover-row
  display: flex
  position: relative
  &.selected
    z-index: 4
    .edit-mode-tab
      @include editModeTabBefore
      display: flex
      &.top
        top: -26px
        border-radius: 5px 5px 0 0
      &.bottom
        bottom: -26px
        border-radius: 0 0 5px 5px
      left: auto
      right: -2px
      background: $om-orange
      &:lang(hu)
        width: 90px
      &:after
        @include editModeTabAfter
      &:lang(hu):after
        width: 90px
  .hoverbar
    display: none
    .fa
      font-family: OmCustom !important
      display: flex
      align-items: center
      width: auto
      height: auto
      padding: 5px 6px
      cursor: pointer
      font-size: 16px !important
      border-radius: 4px
      transition: .3s ease
      &:hover
        background: darken($om-orange, 10%)
  &.dropBefore
    &:before
      @include dropLocation
    &:lang(hu):before
      content: 'Dobd ide az elemet'
  &.dropAfter
    &:after
      @include dropLocation
      top: auto
      bottom: -13px
    &:lang(hu):after
      content: 'Dobd ide az elemet'
  .hoverbar-dashed
    @include absolutePosition
    &.selected
      @include rowActive
      z-index: 3
      &:hover
        @include rowActive
        z-index: 3
  .hoverbar-dashed
  &.selected
    .canv-col
      .col-hoverbar
        @include absolutePosition
        outline: 1px solid $om-orange
    .canv-col.selected
      .col-hoverbar
        @include absolutePosition
        @include colActive
        z-index: 4
    .hoverbar
      display: flex
      @include hoverBar($om-orange)
      border-radius: 5px 5px 0 0
      top: -30px
      left: -2px
      display: none
    &:hover:not(.notDragging)
      .hoverbar
        top: -30px
        left: -2px
  &.hover:not(.notDragging)
    .hoverbar-dashed
      @include rowHover
      z-index: 3
      &.selected
        @include rowActive
        z-index: 3
  &:hover:not(.notDragging)
    .hoverbar-dashed
      @include rowHover
      z-index: 10
      display: flex
      justify-content: center
      align-items: center
      &:before
        opacity: 1
        z-index: 2
        transition: .3s ease
      &.selected
        @include rowActive
        &:before
          display: none
    .hoverbar
      display: flex
      @include hoverBar($om-orange)
      border-radius: 5px 5px 0 0
      top: -30px
      left: -1px
      &:empty
        padding: 0

.om-element
  word-wrap: break-word
  display: flex
  position: relative
  flex-direction: column
  &[draggable="true"]
    cursor: move
    cursor: grab
    cursor: -moz-grab
    cursor: -webkit-grab
    &:active
      cursor: grabbing
      cursor: -moz-grabbing
      cursor: -webkit-grabbing
  &.active
    z-index: 15 !important
  &.selected
    z-index: 4
    position: relative
    .element-hoverbar
      display: none
    &:hover
      z-index: 10 !important
    .full
      z-index: 10
    .om-full
      z-index: 10
  &.hover:not(.notDragging)
    z-index: 4
    .element-hoverbar-dashed
      &.selected
        @include elementActive
        z-index: 1
        &:hover
          @include elementActive
          box-shadow: none
          z-index: 0
  &:hover:not(.notDragging)
    z-index: 13 !important // TODO: investigate why it was buggy with z-index: 10
    .element-hoverbar-dashed
      @include elementHover
      &:before
        opacity: 1
        z-index: 2
        transition: .3s ease
      &.selected
        @include elementActive
        z-index: 1
        &:hover
          @include elementActive
          box-shadow: none
          z-index: 0
        &:before
          display: none
  &.dropBefore
    &:before
      @include dropLocation
    &:lang(hu):before
      content: 'Dobd ide az elemet'
    &:hover:not(.notDragging)
      .element-hoverbar-dashed
        &:before
          display: none
    & ~ .om-element
      &:hover:not(.notDragging)
        z-index: 666 !important
        .element-hoverbar-dashed
          &:before
            display: none
  &.dropAfter
    &:after
      @include dropLocation
      top: auto
      bottom: -13px
    &:lang(hu):after
      content: 'Dobd ide az elemet'
    &:hover:not(.notDragging)
      .element-hoverbar-dashed
        &:before
          display: none
    & ~ .om-element
      &:hover:not(.notDragging)
        .element-hoverbar-dashed
          &:before
            display: none
  .paint-tool-icon
    width: 24px
    display: flex
    justify-content: center
    align-items: center
  .element-hoverbar
    display: none
    @include hoverBar($om-orange)
    font-size: 15px !important
    @media screen and (max-width: 576px)
      font-size: 13px !important
    &:empty
      padding: 0
    .fa
      font-family: OmCustom !important
      display: flex
      align-items: center
      width: auto
      height: auto
      padding: 3px 4px
      cursor: pointer
      border-radius: 4px
      transition: .3s ease
      margin-top: -1px
      align-self: center
      &:hover
        background: darken($om-orange, 10%)
        align-self: center
  &:hover
    .element-hoverbar
      display: flex
      border-radius: 5px 5px 0 0
      right: -1px
      &.top
        top: -26px
        border-radius: 5px 5px 0 0
        &.stacked
          left: 0
          right: auto
      &.bottom
        bottom: -26px
        border-radius: 0 0 5px 5px
        &.stacked
          left: 0
          right: auto
      .paint-tool-icon
        display: flex
        justify-content: center
        align-items: center
        width: 24px
        height: 20px
        margin-top: -1px
        @media screen and (max-width: 576px)
          height: 18px
        .ds-tooltip
          svg
            fill: white !important
          &:hover
            display: flex
            justify-content: center
            align-items: center
            width: 24px
            height: 20px
            background: #d14212
            border-radius: 4px
            cursor: pointer
      .disabled
        cursor: not-allowed !important
        opacity: 0.5
        pointer-events: none
        &:hover
          background: $om-orange
  &.selected:hover
    .element-hoverbar
      display: flex
      right: -1px
      &.top
        top: -27px
        border-radius: 5px 5px 0 0
      &.bottom
        bottom: -27px
        border-radius: 0 0 5px 5px
      .paint-tool-icon
        width: 24px
        justify-content: center
        align-items: center
        svg
          fill: white !important
[type="OmText"],[type="OmButton"],[type="OmImage"], [type="OmInput"], [type="OmFloatingImage"]
  .element-hoverbar-dashed
    @include absolutePosition
    opacity: 0.5
.element-hoverbar-dashed
  @include absolutePosition
  &.selected
    @include elementActive
    @include absolutePositionSelected
    z-index: 1
    &:hover
      @include elementActive
      @include absolutePositionSelected
      box-shadow: none
      z-index: 0
  &.element-resize-in-progress
    @include elementActive
    @include absolutePositionSelected
    z-index: 1
#editor-mode,.editor-mode
  overflow: unset !important
  .om-overlay:not(.om-sidebar)
    .om-overlay-center
      overflow-x: hidden
  .mobile-preview:not(.is-fullscreen)
    margin: -1px
    .om-overlay-center
      padding: 2px
  .om-overlay-center
    z-index: 0
  .om-element
    z-index: 2
    .element-hoverbar-dashed
      &:before
        z-index: 4
    &.selected
      z-index: 4
      &:hover
        z-index: 10 !important
      .full
        z-index: 10
      .om-full
        z-index: 10
    &.hover:not(.notDragging)
      z-index: 4
      .element-hoverbar-dashed
        &.selected
          z-index: 1
          &:hover
            z-index: 0
    &.selected
      &[type="OmFloatingImage"]
        z-index: 11
    &:hover:not(.notDragging)
      z-index: 11 !important
      .element-hoverbar-dashed
        &:before
          z-index: 2
        &.selected
          z-index: 1
          &:hover
            z-index: 0
.canv-col
  position: relative
  &.dropAfter
    &:after
      @include dropLocation
      top: 0
    &:lang(hu):after
      content: 'Dobd ide az elemet'
.col-hoverbar
  @include absolutePosition
  padding: 0

.backyard-near
  &-row-resizer
    &-top,
    &-bottom
      &.hoverbar-active
        z-index: 0
  &-element-hoverbar.hoverbar-active
    z-index: 0
  &-row-hoverbar,
  &-element-hoverbar
    z-index: 110
    position: absolute
    height: 5px
    top: -2px

  &-row-hoverbar
    left: 0
    width: 30px

  &-element-hoverbar
    right: 0
    width: 50%

/* /om hover, bar */

.om-column-min-height-helper
  display: inline-block
  position: relative
  padding: 0
  margin: 0
  width: 100%
  height: fit-content

.paint-tool-icon
  .ds-tooltip
    svg
      fill: white !important
