<template lang="pug">
.content
  .vertical-time-line
    .step
      .circle-holder
        .number 1
      .d-flex.container
        .title {{ $t('integrationFlow.integrationModal.general.copyApiKey') }}
        .description
          a(href="https://cp.selzy.com/en/v5/user/info/api" target="_blank") {{ $t('integrationFlow.integrationModal.general.goToApiKey') }}
    .step
      .circle-holder
        .number 2
      .d-flex.container
        .title {{ $t('integrationFlow.integrationModal.general.pasteApiKey') }}
        .description
          .input-holder
            om-input#apiKey.w-100(v-model.trim="settings.apiKey" type="text")
              template(#error v-if="validations.apiKey.$error && !validations.apiKey.required")
                span {{ $t('integrationFlow.integrationModal.general.apiKeyError') }}
              template(#error v-if="validations.apiKey.$error && !validations.apiKey.validByAlert")
                span {{ $t('integrationFlow.integrationModal.general.apiKeyInvalid') }}
              template(#success v-if="validByAlert.apiKeyFixed")
                span {{ $t('integrationFlow.integrationModal.general.apiKeyFixed') }}
    .step
      .circle-holder
        .number 3
      integration-name(:name.sync="settings.name" :validations="validations")
</template>
<script>
  import IntegrationName from '@/components/IntegrationModals/IntegrationName';

  export default {
    components: {
      IntegrationName,
    },

    props: {
      settings: {
        type: Object,
        required: true,
      },

      validations: {
        type: Object,
        required: true,
      },

      validByAlert: {
        type: Object,
        required: true,
      },
    },
  };
</script>
