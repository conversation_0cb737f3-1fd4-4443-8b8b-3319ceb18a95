<template lang="pug">
om-modal(
  name="name-campaign"
  modalClasses="om-name-campaign-modal"
  :width="500"
  headerBorder
  :clickToClose="!isUpdateMode"
  @beforeOpen="beforeOpen"
  @beforeClose="beforeClose"
  @closed="$emit('close')"
)
  template(slot="modal-header")
    .row
      .col
        om-heading(h5) {{ getDomainInputModalTitle }}
    .brand-modal-action-icon.cursor-pointer(@click="close")
      close-icon(:width="12" :height="12" color="#AAB1C1")
  template(slot="modal-body")
    domain-input-v2(
      @selectedDomain="onSelectedDomain"
      :savedExternally="saveBtnClickCounter"
      @saveEvent="saveCampaign"
      :isDynamicContent="isDynamicContent"
    )
  template(slot="modal-footer")
    .d-flex.justify-content-center.align-items-center(style="flex-flow: column")
      om-button.mt-4(
        primary
        :loading="loading"
        @click="save"
        :data-track="isUpdateMode ? 'domain next' : 'domain update'"
      ) {{ isUpdateMode ? $t('save') : $t('next') }}
      .mt-3(v-if="canChange" style="color: #c4c8cd") {{ $t('newCampaignFlow.domain.canChange') }}
</template>
<script>
  import CHANGE_CAMPAIGN_SETTINGS from '@/graphql/ChangeCampaignSettings.gql';
  import DomainInputV2 from '@/components/DomainInputV2.vue';
  import { getAccountIdFromCookie } from '@/util';
  import { get as _get } from 'lodash-es';
  import { required } from 'vuelidate/lib/validators';
  import { mapGetters, mapMutations, mapState, mapActions } from 'vuex';
  import { createDCUrl } from '@/utils/pncURLBuilder';
  import { getRedirectDomain } from '@/utils/dcHelpers';

  export default {
    components: {
      DomainInputV2,
    },

    data() {
      return {
        selectedDomain: null,
        loading: false,
        saveBtnClicked: false,
        saveBtnClickCounter: 0,
        mode: null,
        canChange: true,
        sabOnly: false,
        isBlank: false,
      };
    },

    computed: {
      ...mapGetters(['brandName', 'domains', 'isSuperAdmin', 'isNewFrequencyRuleEnabled']),
      ...mapGetters('campaignCreation', ['showMiniWizard']),
      ...mapState(['account']),
      ...mapState('campaignCreation', [
        'templateId',
        'colors',
        'theme',
        'needsSMSPrompt',
        'isDynamicContent',
        'addControlVariant',
      ]),
      getDomainInputModalTitle() {
        if (this.isDynamicContent) {
          return this.$t('newCampaignFlow.dcDomainSelect');
        }

        return this.$t('newCampaignFlow.domainSelect', { brand: this.brandName });
      },
      isUpdateMode() {
        return this.mode === 'update';
      },
      isOnboarding() {
        return this.$route.fullPath.includes('/onboarding');
      },
    },

    validations: {
      selectedDomain: {
        required,
      },
    },
    mounted() {
      this.$bus.$on('save-campaign', async ({ selectedDomain, domain }) => {
        this.selectedDomain = selectedDomain;
        await this.saveCampaignRaw(domain);
      });
    },
    beforeDestroy() {
      this.$bus.$off('save-campaign');
    },

    methods: {
      ...mapMutations(['changeFormManagerVisibility']),
      ...mapMutations('campaignCreation', [
        'setDomain',
        'setSource',
        'setSourceUrl',
        'setCurrentStep',
      ]),
      ...mapActions('campaignCreation', [
        'createCampaign',
        'generateSiteData',
        'initCampaignCreation',
      ]),
      beforeOpen(event) {
        this.setCurrentStep('name-campaign');
        this.selectedDomain = null;
        this.saveBtnClicked = false;
        this.saveBtnClickCounter = 0;

        if (event.params) {
          this.mode = event.params.mode || null;
          this.canChange = event.params.canChange ?? true;
          this.sabOnly = event.params.sabOnly ?? false;
          this.isBlank = event.params.blank ?? false;

          this.initCampaignCreation({
            templateId: event.params.templateId,
            colors: event.params.colors,
            theme: event.params.theme,
            needsSMSPrompt: event.params.needsSMSPrompt,
            isDynamicContent: event.params.isDynamicContent,
            addControlVariant: event.params.addControlVariant,
          });
        }

        this.loading = false;

        if (this.isUpdateMode) {
          // remove ESC listener to prevent modal close
          window.removeEventListener('keyup', this.$children[0].$children[0].onEscapeKeyUp);
        }
      },

      beforeClose() {
        if (this.isUpdateMode) {
          // add ESC listener to restore og functionality
          window.addEventListener('keyup', this.$children[0].$children[0].onEscapeKeyUp);
        }
        window.history.replaceState(null, null, ' ');
      },

      save() {
        if (this.saveBtnClicked) {
          return;
        }

        this.saveBtnClickCounter++;
      },
      validateDomainForDC() {
        const { _id } = this.selectedDomain;
        const domainSettings = this.domains.find(({ _id: domainId }) => domainId === _id);
        if (!domainSettings) return false;

        const fromDate = parseInt(new Date(domainSettings?.v3LastRequestDate).getTime() / 1000, 10);
        const toDate = parseInt(new Date().getTime() / 1000, 10);
        const diff = parseInt((toDate - fromDate) / 3600, 10);
        const MAX_DIFF_IN_HOURS = 168;
        if (diff > MAX_DIFF_IN_HOURS) {
          return false;
        }
        return true;
      },
      async saveCampaignRaw(domain) {
        if (!this.selectedDomain) return;

        try {
          let source = '';
          let sourceUrl = '';
          if (this.$route.path.includes('use-case')) {
            source = 'use-case';
            sourceUrl = `?source=use-case&use-case-id=${this.$route.params.slug}`;
          }

          this.setDomain(this.selectedDomain);
          this.setSource(source);
          this.setSourceUrl(sourceUrl);
          if (this.showMiniWizard && !this.isBlank) {
            this.generateSiteData(this.selectedDomain._id);
          }

          if (this.needsSMSPrompt) {
            this.$modal.hide('name-campaign');
            this.$modal.show('new-campaign-sms-prompt');
            window.history.pushState(undefined, undefined, '#email-sms');
            return;
          }

          if (this.showMiniWizard && !this.isBlank) {
            this.$modal.hide('name-campaign');
            this.$modal.show('mini-wizard-auto-personalize');
            window.history.pushState(undefined, undefined, '#auto-personalize');
            return;
          }

          const r = await this.createCampaign();

          const campaignId = _get(r, 'data.createCampaign.id');
          const variantId = _get(r, 'data.createCampaign.variants.0._id');

          if (!campaignId || !variantId) {
            throw new Error('Failed to create campaign');
          }

          this.loading = false;

          if (this.isDynamicContent) {
            const [variant] = _get(r, 'data.createCampaign.variants');
            const campaignDomain = _get(r, 'data.createCampaign.domain');
            const url = this.getDynamicContentUrl(
              getRedirectDomain(domain?.originalInput, campaignDomain),
              campaignId,
              this.$i18n.locale,
              variant._id,
              domain?.pathName ? domain.pathName : '',
            );

            window.open(url, '_self');
          } else {
            window.location = `/${getAccountIdFromCookie()}/variant/${campaignId}/${variantId}/edit/new${sourceUrl}`;
          }
        } catch (e) {
          this.loading = false;
          this.saveBtnClicked = false;
          console.log(e);
          this.$notify({
            type: 'error',
            text: this.$t('notifications.saveError'),
          });
        }
      },
      getDynamicContentUrl(campaignDomain, campaignId, locale, variantId, pathName) {
        const returnActiveBox = this.isNewFrequencyRuleEnabled ? 'frequency' : 'condition';
        return createDCUrl({
          domain: campaignDomain,
          campaignId,
          locale,
          variantId,
          path: pathName,
          returnUrl: `/${this.account.databaseId}/campaign/${campaignId}/settings?activeBox=${returnActiveBox}`,
          backUrl: `/${this.account.databaseId}/campaign/${campaignId}`,
          isNew: true,
          isSuperAdmin: this.isSuperAdmin,
          featureMode: this.sabOnly ? 'smart-ab-test' : null,
        });
      },

      async saveCampaign(domain) {
        if (domain?.selectedDomain) {
          this.selectedDomain = domain.selectedDomain;
        }

        this.$v.$touch();
        if (this.$v.$invalid) {
          this.$notify({
            type: 'error',
            text: this.$t('notifications.selectOrAddDomain'),
          });
          return;
        }

        if (this.isUpdateMode) return this.updateCampaignDomain();

        this.saveBtnClicked = true;
        this.loading = true;

        if (this.isDynamicContent) {
          const isDomainCorrect = this.validateDomainForDC();
          if (!isDomainCorrect && !this.isOnboarding) {
            this.loading = false;
            this.$modal.hide('name-campaign');
            this.$modal.show('last-request-date-old', {
              selectedDomain: this.selectedDomain,
              domain,
            });
            return;
          }
        }
        await this.saveCampaignRaw(domain);
      },
      async updateCampaignDomain() {
        const { _id: domainId, domain } = this.selectedDomain;
        const { campaignId } = this.$store.state.campaign;
        try {
          const { data: result } = await this.$apollo.mutate({
            mutation: CHANGE_CAMPAIGN_SETTINGS,
            variables: {
              input: {
                _id: campaignId,
                domainId,
                domain,
              },
            },
          });

          if (result) {
            const {
              changeCampaignSettings: { success, message },
            } = result;

            if (success) {
              this.$store.state.campaign.domain = domain;
              this.$emit('domainUpdated');
              this.$modal.hide('name-campaign');
              return;
            }
            this.$notify({
              type: 'error',
              text: message || this.$t('notifications.saveError'),
            });
          } else {
            this.$notify({
              type: 'error',
              text: this.$t('notifications.saveError'),
            });
          }
        } catch (e) {
          console.log('@@@error', { e });
          this.$notify({
            type: 'error',
            text: this.$t('notifications.saveError catch'),
          });
        }
      },
      onSelectedDomain(domain) {
        // reset double click detection on domain change
        this.saveBtnClicked = false;
        this.selectedDomain = domain;
        // if (this.selectedDomain) setTimeout(() => this.saveCampaign(), 100)
      },
      close() {
        this.setCurrentStep(null);
        this.$modal.hide('name-campaign');
      },
    },
  };
</script>
<style lang="sass">
  .om-name-campaign-modal
    .brand-modal-footer
      padding-top: 0 !important
    .brand-modal-body
      padding-left: 40px !important
      padding-right: 40px !important
      padding-bottom: 0 !important
</style>
