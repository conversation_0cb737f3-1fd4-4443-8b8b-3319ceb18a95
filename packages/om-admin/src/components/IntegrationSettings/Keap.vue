<template lang="pug">
div
  .mb-4
    .row.align-items-center.mb-2
      .col-sm-4.col-form-label
        label {{ $t('integrations.keap.fields.tags') }}
      .col-sm-8
        multiselect(
          v-model="selectedTags"
          :options="tags"
          :multiple="true"
          label="name"
          track-by="id"
          :taggable="false"
          :show-labels="false"
          :placeholder="$t('itemSelected', { num: selectedTags.length })"
          :closeOnSelect="false"
        )
          template(slot="clear" slot-scope="props")
            .multiselect__clear {{ $t('itemSelected', { num: selectedTags.length }) }}
          template(slot="tag" slot-scope="props")
            span
  integration-binding(
    :type="globalIntegration.type"
    :bindings.sync="bindings"
    :fields.sync="fields"
  )
</template>

<script>
  import GET_INTEGRATION_DATA from '@/graphql/GetIntegrationData.gql';
  import IntegrationBinding from '@/components/IntegrationBinding.vue';
  import integrationSetting from '@/mixins/integrationSetting';

  export default {
    components: {
      IntegrationBinding,
    },
    mixins: [integrationSetting],

    data() {
      return {
        tags: [],
        selectedTags: [],
        fields: [],
        settings: {
          tags: '',
        },
      };
    },

    watch: {
      selectedTags(tags) {
        this.settings.tags = tags.map((t) => t.id).join('|');
      },
    },

    apollo: {
      integrationData: {
        query: GET_INTEGRATION_DATA,
        variables() {
          return {
            integrationType: this.globalIntegration.type,
            integrationId: this.globalIntegration._id,
          };
        },
        result({
          data: {
            integrationData: { tags, fields },
          },
        }) {
          this.tags = tags;
          this.fields = fields;

          if (this.settings.tags) {
            const tagIds = this.settings.tags.split('|');
            this.selectedTags = this.tags.filter((t) => tagIds.includes(t.id.toString()));
          } else {
            this.settings.tags = '';
          }

          this.$emit('loaded');
        },
      },
    },
  };
</script>
