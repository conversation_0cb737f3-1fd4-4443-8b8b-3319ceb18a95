<template lang="pug">
.seasonal-recommender
  template(v-if="onboardingFinished")
    skeleton.mt-2(v-if="loading" :rows="1" :cols="1")
    template(v-else)
      img.mb-4.w-100.image(
        :alt="$t(banner.title)"
        :src="banner.img"
        :class="clickableClasses"
        @click="openTemplateCollection"
      )
      .d-flex.mb-4
        om-heading(h5 :class="clickableClasses" @click.native="openTemplateCollection") {{ $t(`onboarding.seasonal.${banner.key}.title`) }}
      om-body-text.gray-600(bt400md v-html="$t(`onboarding.seasonal.${banner.key}.description`)")
      .d-flex.justify-content-end.mt-5
        om-button.mr-2(secondary @click="openTemplateCollection") {{ $t('onboarding.seasonal.showTemplates') }}
  .d-block(v-else)
    img.mb-4.w-100(
      alt="Seasonal banner placeholder"
      src="@/assets/admin/img/onboarding/dashboard/tactic_placeholder.png"
    )
    .text-center.px-2.seasonal-recommender__placeholder-text {{ $t('onboarding.tactic.placeholder') }}
</template>

<script>
  import { track } from '@/services/xray';
  import { mapGetters, mapActions, mapMutations } from 'vuex';
  import Skeleton from '@/components/TemplateChooser/components/Skeleton.vue';
  import commonFlowsMixin from '@/mixins/commonFlows';

  export default {
    name: 'SeasonalRecommender',
    components: {
      Skeleton,
    },
    mixins: [commonFlowsMixin],

    props: {
      onboardingFinished: {
        type: Boolean,
        default: false,
      },
      isLoading: {
        type: Boolean,
        default: false,
      },
      locale: {
        type: String,
        default: 'hu',
      },
    },

    data: () => ({
      banner: {},
      loading: false,
      about: 'sab',
    }),

    computed: {
      ...mapGetters(['getSeasonalBanner', 'getLocale']),

      clickableClasses() {
        return 'cursor-pointer clickable-title';
      },
    },

    async created() {
      this.loading = true;
      await this.fetchSeasonalBanner(this.locale);
      this.banner = this.getSeasonalBanner;
      this.setSeasonalBanner(this.banner);
      this.loading = false;
      track('get_seasonal_banner', {
        banner: this.banner,
      });
    },
    methods: {
      ...mapActions(['fetchSeasonalBanner']),
      ...mapMutations(['setSeasonalBanner']),
      openTemplateCollection() {
        this.$router.push({
          name: 'seasonal-templates',
          params: {
            userId: this.$route.params.userId,
            slug: this.banner.key,
          },
        });
      },
    },
  };
</script>

<style lang="sass">
  @import '@/sass/variables/_colors.sass'

  .seasonal-recommender
    width: 18.75rem
    height: 100%
    .body-text
      line-break: auto

    &__placeholder-text
      line-height: 28px
      color: #8F97A4
      font-size: 1rem
      font-weight: 500

    .image
      height: 100%
      object-fit: contain

  .clickable-title
    transition: color 200ms ease-in-out
    &:hover
      color: $om-orange-500
</style>
