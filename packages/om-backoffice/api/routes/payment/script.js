const { Router } = require('express');

const router = Router({ mergeParams: true });
const cronTokenAuth = require('../../mw/cronTokenAuth');
const flexiPayScriptController = require('../../controllers/payment/flexipay/script');
const {
  updateFreePackagePV,
} = require('../../controllers/payment/pricing2024/freePackageUpdateScript');
const {
  updateCurrentPackage,
} = require('../../controllers/payment/pricing2024/currentPackageUpdateScript');
const {
  updateCurrentPackageRevert,
} = require('../../controllers/payment/pricing2024/currentPackageUpdateRevertScript');
const { createProducts } = require('../../controllers/payment/pricing2024/createProducts');
const { limitUsageReport } = require('../../controllers/payment/report/limitUsage');

router.get(
  '/flexipay/set-default-flexipay-maximum-total',
  cronTokenAuth,
  flexiPayScriptController.setDefaultMaximumTotals,
);

router.get('/pricing-2024/create-products', cronTokenAuth, createProducts);
router.get('/pricing-2024/update-free-package-pv', cronTokenAuth, updateFreePackagePV);
router.get('/pricing-2024/update-current-package', cronTokenAuth, updateCurrentPackage);
router.get(
  '/pricing-2024/update-current-package-revert',
  cronTokenAuth,
  updateCurrentPackageRevert,
);

router.get('/limit-usage-report', cronTokenAuth, limitUsageReport);

module.exports = router;
