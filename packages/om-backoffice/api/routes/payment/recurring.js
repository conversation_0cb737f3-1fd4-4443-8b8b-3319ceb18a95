const { Router } = require('express');

const router = Router({ mergeParams: true });
const cronTokenAuth = require('../../mw/cronTokenAuth');
const shopifyRecurringController = require('../../controllers/payment/shopify/recurring');
const bankTransferRecurringController = require('../../controllers/payment/bank-transfer/recurring');
const braintreeRecurringController = require('../../controllers/payment/braintree/recurring');

router.get('/shopify', cronTokenAuth, shopifyRecurringController.checkAccounts);
router.get('/braintree', cronTokenAuth, braintreeRecurringController.recurringPayment);
router.get(
  '/bank-transfer',
  cronTokenAuth,
  bankTransferRecurringController.generateProformaInvoices,
);

module.exports = router;
