const { STATUS } = require('./enums');

// date_added alapján csö<PERSON>ken<PERSON>ben kell lennie, hogy jól mű<PERSON>jön
const setUpEnabledActionsForItems = (rows) => {
  const helper = { hasPayout: false };
  rows.forEach((item) => {
    item.enabledActions = [];
    item.connectedCloseItem = null;

    // Régről vannak olyan orderek, amiket még manuálisan kell validálni
    // Csak olyanoknál szabad engedni ezt, amelyek után nem volt payout
    if (!helper.hasPayout && item.type === 'order' && item.status === STATUS.COMMISSION_PENDING) {
      item.enabledActions.push('manual-validate');
    }

    // Csak olyanok modify tételeket lehet törölt státuszra tenni, amelyek után nem volt payout
    if (!helper.hasPayout && item.type === 'modify' && item.status === STATUS.MODIFICATION_VALID) {
      item.enabledActions.push('delete-modify');
    }

    // Csak olyanok payout tételeket lehet törölt státuszra tenni és szerkeszteni,
    // amelyek még pending-ek, és amelyek után nem volt payout
    if (!helper.hasPayout && item.type === 'payout' && item.status === STATUS.PAYOUT_PENDING) {
      item.enabledActions.push('delete-payout');
      item.enabledActions.push('edit-payout');
    }

    // Törölt payout adatlapja nem megtekinthető
    if (item.type === 'payout' && item.status !== STATUS.PAYOUT_DELETED) {
      item.enabledActions.push('view-payout');
    }

    // Payout itemhez close item csatolás ha van
    if (item.type === 'payout' && item.source_id) {
      item.connectedCloseItem = item.source_id;
    }

    // Nem törölt státuszú payout - utáni lévő elemeknél ez alapján lehet korlátozás
    if (item.type === 'payout' && item.status !== STATUS.PAYOUT_DELETED) {
      helper.hasPayout = true;
    }
  });
  return rows;
};

const setUpEnabledActionsForItem = (item, lastPayout) => {
  item.enabledActions = [];
  item.connectedCloseItem = null;

  if (item.type === 'payout') {
    if (item.status === STATUS.PAYOUT_PENDING && item.item_id === lastPayout.item_id) {
      item.enabledActions.push('delete-payout');
      item.enabledActions.push('edit-payout');
    }

    if (item.status !== STATUS.PAYOUT_DELETED) {
      item.enabledActions.push('view-payout');
    }

    // Payout itemhez close item csatolás ha van
    if (item.source_id) {
      item.connectedCloseItem = item.source_id;
    }
  }
  return item;
};

module.exports = {
  setUpEnabledActionsForItems,
  setUpEnabledActionsForItem,
};
