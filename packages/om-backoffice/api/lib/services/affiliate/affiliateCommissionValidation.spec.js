const { AffiliateCommissionValidation } = require('./affiliateCommissionValidation');
const { AffiliateLogger } = require('./affiliateLogger');
const { STATUS, ACCOUNTED_STATUS } = require('./enums');
const orderRepository = require('../../repositories/orderRepository');

const DEFAULT_DATABASE_AND_CUSTOMER_ID = 1;

const initSpies = (items, orders) => {
  jest.spyOn(AffiliateLogger.prototype, '_insertActivityLog').mockImplementation(() => {
    return null;
  });

  jest.spyOn(AffiliateCommissionValidation.prototype, '_getCustomerId').mockImplementation(() => {
    return DEFAULT_DATABASE_AND_CUSTOMER_ID;
  });

  jest
    .spyOn(AffiliateCommissionValidation.prototype, '_getValidCommissionItems')
    .mockImplementation(() => {
      return items;
    });

  jest
    .spyOn(AffiliateCommissionValidation.prototype, '_getOrderStatusesForItems')
    .mockImplementation(() => {
      return orders;
    });

  jest.spyOn(AffiliateCommissionValidation.prototype, '_insertItem').mockImplementation((data) => {
    return null;
  });

  jest
    .spyOn(AffiliateCommissionValidation.prototype, '_updateItem')
    .mockImplementation((id, data) => {
      return null;
    });

  return {
    updateItemSpy: AffiliateCommissionValidation.prototype._updateItem,
    insertItemSpy: AffiliateCommissionValidation.prototype._insertItem,
  };
};

beforeEach(async () => {
  jest.clearAllMocks();
});

describe('Affiliate commission validation', () => {
  test('All commissions are valid', async () => {
    const databaseId = DEFAULT_DATABASE_AND_CUSTOMER_ID;
    const validStatuses = [
      orderRepository.ORDER_STATUS.FULFILLED,
      orderRepository.ORDER_STATUS.CREDIT_CARD_FULFILLED,
      orderRepository.ORDER_STATUS.FULFILLED_SHOPIFY,
    ];
    const items = [];
    const orders = [];
    for (const status of validStatuses) {
      items.push({
        item_id: 1,
        customer_id: 1,
        type: 'order',
        total: 1000,
        order_total: 10000,
        source_id: 1,
        status: STATUS.COMMISSION_VALID,
        accounted: ACCOUNTED_STATUS.RAW,
      });
      orders.push({
        order_id: 1,
        order_status_id: status,
      });
    }

    const { updateItemSpy, insertItemSpy } = initSpies(items, orders);

    const validationInstance = new AffiliateCommissionValidation(databaseId);
    const result = await validationInstance.runCommissionValidation();

    expect(result.summary).toMatchObject({ count: 0, itemIds: [], orderIds: [], total: 0 });

    expect(updateItemSpy).toHaveBeenCalledTimes(0);
    expect(insertItemSpy).toHaveBeenCalledTimes(0);
  });

  test('Refund', async () => {
    const databaseId = DEFAULT_DATABASE_AND_CUSTOMER_ID;
    const items = [
      {
        item_id: 1,
        customer_id: 1,
        type: 'order',
        total: 1000,
        order_total: 10000,
        source_id: 1,
        status: STATUS.COMMISSION_VALID,
        accounted: ACCOUNTED_STATUS.RAW,
      },
    ];
    const orders = [
      {
        order_id: 1,
        order_status_id: 0,
      },
    ];

    const { updateItemSpy, insertItemSpy } = initSpies(items, orders);

    const validationInstance = new AffiliateCommissionValidation(databaseId);
    const result = await validationInstance.runCommissionValidation(databaseId);

    expect(result.summary).toMatchObject({ count: 1, itemIds: [1], orderIds: [1], total: 1000 });

    expect(updateItemSpy).toHaveBeenCalledTimes(1);
    expect(updateItemSpy.mock.calls[0][0]).toBe(1);
    expect(updateItemSpy.mock.calls[0][1]).toMatchObject({ status: STATUS.COMMISSION_INVALID });

    expect(insertItemSpy).toHaveBeenCalledTimes(1);
    expect(insertItemSpy.mock.calls[0][0]).toMatchObject({
      customer_id: 1,
      type: 'refund',
      source_id: 1,
      comment: '',
      total: -1000,
      order_total: -10000,
      status: STATUS.REFUND_VALID,
    });
  });
});
