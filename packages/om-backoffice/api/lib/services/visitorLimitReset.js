const moment = require('moment');
const { AccountLimit } = require('@om/queues');
const accountRepository = require('../repositories/accountRepository');
const logger = require('../helpers/infrastructure/logger');
const limitResetLogRepository = require('../repositories/limitResetLogRepository');
const { createUsageReport, sendUsageReport } = require('./subUserUsageReporter');

const RECURRING = {
  default: {
    executeAndExpirationDateDiffInDays: 14,
    cronExecuteUtcHour: 7,
  },
};

const getLogger = () => {
  return logger.child({ service: 'visitor-limit-reset' });
};

const RESET_LIMIT_CRON = {
  executeAndExpirationDateDiffInDays: 14,
  cronExecuteUtcHour: 0,
};

const _formatDate = (date) => {
  return moment(date).format('YYYY-MM-DD');
};

const _getRecurringAndExpirationDateDiffInDays = (paymentMethod) => {
  return (
    RECURRING[paymentMethod]?.executeAndExpirationDateDiffInDays ??
    RECURRING.default.executeAndExpirationDateDiffInDays
  );
};

const _getRecurringCronExecuteUtcHour = (paymentMethod) => {
  return RECURRING[paymentMethod]?.cronExecuteUtcHour ?? RECURRING.default.cronExecuteUtcHour;
};

const _getNextRecurringDate = (dateExpires, paymentMethod, eventDate) => {
  const nextDate = moment(dateExpires)
    .utc()
    .subtract(_getRecurringAndExpirationDateDiffInDays(paymentMethod), 'day')
    .startOf('day')
    .set('hour', _getRecurringCronExecuteUtcHour(paymentMethod));
  if (nextDate.diff(eventDate) < 0) {
    return null;
  }
  return nextDate;
};

const _getNextLimitResetCronDate = (dateExpires, eventDate) => {
  const datePaid = moment(dateExpires)
    .utc()
    .subtract(RESET_LIMIT_CRON.executeAndExpirationDateDiffInDays, 'day')
    .startOf('day');
  const resetDayOfMonth = datePaid.date();
  const nextDate = moment(eventDate)
    .startOf('day')
    .startOf('month')
    .set('date', resetDayOfMonth)
    .set('hour', RESET_LIMIT_CRON.cronExecuteUtcHour);
  if (nextDate.diff(eventDate) < 0 || moment(nextDate).diff(datePaid, 'days') === 0) {
    nextDate.add(1, 'month');
  }
  return nextDate;
};

const shouldResetLimits = (dateExpires, eventDate = null) => {
  const today = eventDate ?? moment.utc().startOf('day');
  const paidUntil = moment.utc(dateExpires).startOf('day').subtract(14, 'days');
  const monthDiff = paidUntil.diff(today, 'months', true);
  const correctedExpires = paidUntil.subtract(monthDiff, 'months');
  const dayDiffFromToday = correctedExpires.diff(today, 'days');

  // console.log({
  //   paidUntil,
  //   correctedExpires,
  //   dayDiffFromToday,
  //   examine: `${monthDiff} > 0 && ${dayDiffFromToday} === 0`,
  //   examineResult: monthDiff > 0 && dayDiffFromToday === 0,
  // });

  return monthDiff > 0 && dayDiffFromToday === 0;
};

const calculateNextLimitResetDate = (dateExpires, paymentMethod, eventDate = null) => {
  eventDate = eventDate ?? moment().utc();
  if (moment(dateExpires).isValid() === false) {
    return '';
  }
  const nextBillingDate = _getNextRecurringDate(dateExpires, paymentMethod, eventDate);
  if (nextBillingDate === null) {
    return '';
  }
  const nextLimitResetCronDate = _getNextLimitResetCronDate(dateExpires, eventDate);
  const nextReset =
    nextBillingDate.unix() < nextLimitResetCronDate.unix()
      ? nextBillingDate
      : nextLimitResetCronDate;
  return _formatDate(nextReset);
};

const getLastLimitResetDate = async (databaseId) => {
  const list = await limitResetLogRepository.getByDatabaseId(databaseId, 50);
  const last = list ? list[0] : '';
  return last?.date ?? '';
};

const getLimitResetLogList = async (databaseId) => {
  const list = await limitResetLogRepository.getByDatabaseId(databaseId, 50);
  return list;
};

const resetAccountVisitorLimits = async (databaseId, isAgency) => {
  const account = await accountRepository.get(databaseId);

  await accountRepository.resetLimits(databaseId);

  if (isAgency) {
    await accountRepository.resetSubAccountLimitsFor(databaseId);
  }

  await AccountLimit.addJob({ databaseId });
  getLogger().info({ message: 'Limit reset inserted', databaseId });
  return limitResetLogRepository.insert(account);
};

const resetLimitsForAccounts = async (accounts) => {
  const dbIds = accounts.map(({ databaseId }) => databaseId);
  let result;
  const agencyUsageReports = [];
  const limitResetsProm = [];

  try {
    for (const account of accounts) {
      if (account.type === 'agency') {
        const agencyUsageReportRow = {
          accountId: account.databaseId,
          statId: null,
          sendSuccess: false,
        };
        const statId = await createUsageReport(account);
        agencyUsageReportRow.statId = statId;
        await accountRepository.resetSubAccountLimitsFor(account.databaseId);
        agencyUsageReportRow.sendSuccess = await sendUsageReport(account, statId);
        agencyUsageReports.push(agencyUsageReportRow);
      }
      limitResetsProm.push(AccountLimit.addJob({ databaseId: account.databaseId }));
      await limitResetLogRepository.insert(account);
      getLogger().info({ message: 'Limit reset inserted', databaseId: account.databaseId });
    }

    await accountRepository.resetLimitsForAccounts(dbIds);
    await Promise.all(limitResetsProm);
    result = { success: true, error: null, agencyUsageReports };
  } catch (e) {
    result = { success: false, error: e, agencyUsageReports };
  }

  return result;
};

module.exports = {
  shouldResetLimits,
  calculateNextLimitResetDate,
  getLastLimitResetDate,
  getLimitResetLogList,
  resetAccountVisitorLimits,
  resetLimitsForAccounts,
};
