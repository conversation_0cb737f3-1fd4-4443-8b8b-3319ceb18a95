const Redis = require('ioredis');

const REDIS_HOST = process.env.REDIS_HOST;

const redis = REDIS_HOST ? new Redis(6379, REDIS_HOST) : null;

const DIGICEL_DBID = 10610;
const TURBODIETA_DBID = 112649;
const TROISSOIXANTE_DBID = 137506;
const PREMIUMSHOP24_DBID = 190738;
const ROADRECORD_DBID = 199139;

const getUniqueDigicelKey = () => `uniqueVisitor:digicel`;
const getUniqueTurbodietaKey = () => `uniqueVisitor:turbodieta`;
const getTroisSoixanteSubKey = () => `uniqueVisitor:triosSoixanteSub137506`;
const getPremiumShop24Key = () => `uniqueVisitor:premiumshop24`;
const getRoadRecordKey = () => `uniqueVisitor:roadRecord`;
const getUniqueVisitorKey = (accountId) => `uniqueVisitor:${accountId}`;

const inited = () => redis !== null;
const del = (key) => {
  return redis.del(key);
};

const get = (key) => {
  return redis.get(key);
};

const set = (key, value) => {
  return redis.set(key, value);
};

const setex = (key, value, ttl) => {
  return redis.setex(key, ttl, value);
};

const deleteDigicelUniqueVisitorCache = () => redis.del(getUniqueDigicelKey());
const deleteTurbodietaUniqueVisitorCache = () => redis.del(getUniqueTurbodietaKey());
const deleteTroisSoixanteSubVisitorCache = () => redis.del(getTroisSoixanteSubKey());
const deletePremiumShop24UniqueVisitorCache = () => redis.del(getPremiumShop24Key());
const deleteRoadRecordVisitorCache = () => redis.del(getRoadRecordKey());
const deleteUniqueVisitorCache = (accountId) => redis.del(getUniqueVisitorKey(accountId));

module.exports = {
  redis,
  get,
  del,
  inited,
  set,
  setex,
  deleteDigicelUniqueVisitorCache,
  deleteTurbodietaUniqueVisitorCache,
  deleteTroisSoixanteSubVisitorCache,
  deletePremiumShop24UniqueVisitorCache,
  deleteRoadRecordVisitorCache,
  deleteUniqueVisitorCache,
  DIGICEL_DBID,
  TURBODIETA_DBID,
  TROISSOIXANTE_DBID,
  PREMIUMSHOP24_DBID,
  ROADRECORD_DBID,
};
