const { knex } = require('../helpers/infrastructure/mysql');

const LOG_TABLE = 'log';

const log = (category, uniqueId, type, subType, databaseId, extra) => {
  return knex(LOG_TABLE).insert({
    unique_id: uniqueId,
    category,
    date_created: new Date(),
    type,
    sub_type: subType,
    user_id: databaseId,
    extra: JSON.stringify(extra),
  });
};

const info = async (uniqueId, type, subType, extra, databaseId = 0) => {
  return log('INFO', uniqueId, type, subType, databaseId, extra);
};

const error = async (uniqueId, type, subType, extra, databaseId = 0) => {
  return log('ERROR', uniqueId, type, subType, databaseId, extra);
};

module.exports = {
  info,
  error,
};
