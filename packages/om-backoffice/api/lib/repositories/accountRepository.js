/* eslint-disable no-use-before-define */
const moment = require('moment');
const _ = require('lodash');
const { ObjectID } = require('mongodb');
const { PARTNER_TYPES, ACCOUNT_TYPES } = require('@om/payment/src/partnerType');
const { getCollectionRef } = require('../../../util/mongo');
const logger = require('../helpers/infrastructure/logger');
const accountHelper = require('../helpers/account');
const planDetails = require('../helpers/planDetails');
const productRepository = require('./productRepository');
const expiringAccounts = require('./accountRepository/expiringAccounts');
const userActivityLogRepository = require('./userActivityLogRepository');

const COLL_REF = 'accounts';
const LOGINS_REF = 'logins';
const COUNTER_REF = 'counter';

const resetLimitsForAll = (_ids) => {
  return getCollectionRef(COLL_REF).update(
    { _id: { $in: _ids } },
    {
      $set: {
        'limits.usedVisitor': 0,
        'limits.pageViews': 0,
        'limits.limitReachedAt': null,
        'limits.limit80EmailSent': false,
        'limits.limitReachedEmailSent': false,
        'limits.salesPinged': false,
      },
    },
    { multi: true },
  );
};

const count = () => {
  return getCollectionRef(COLL_REF).countDocuments();
};

const list = (fields, filter = {}) => {
  const projection = {};
  for (const field of fields) {
    projection[field] = 1;
  }
  filter.deleted = false;

  return getCollectionRef(COLL_REF)
    .find(filter, Object.keys(projection).length ? { projection } : {})
    .toArray();
};

const get = (databaseId) => {
  return getCollectionRef(COLL_REF).findOne({
    databaseId: parseInt(databaseId, 10),
  });
};

const getByObjectId = (_id) => {
  if (ObjectID.isValid(_id)) {
    _id = new ObjectID(_id);
  }

  return getCollectionRef(COLL_REF).findOne({ _id });
};

const getByEmail = (email) => {
  return getCollectionRef(COLL_REF).findOne({
    name: email,
  });
};

const getAccountWithRegion = async (databaseId) => {
  let account = await getCollectionRef(COLL_REF)
    .aggregate([
      { $match: { databaseId: Number(databaseId) } },
      {
        $lookup: {
          from: 'master_logins',
          foreignField: '_id',
          localField: 'users.0.loginId',
          as: 'logins',
        },
      },
      {
        $project: {
          databaseId: 1,
          type: 1,
          name: 1,
          features: 1,
          createdAt: 1,
          billing: 1,
          limits: 1,
          users: 1,
          regions: '$logins.region',
        },
      },
    ])
    .toArray();

  if (account.length) {
    account = account[0];
  } else {
    return false;
  }

  return {
    id: account.databaseId,
    type: account.type,
    email: account.name,
    package: account.billing.package,
    futurePackage: account.billing.futurePackage,
    period: account.billing.period,
    datePaid: account.billing.datePaid,
    dateExpires: account.billing.dateExpires,
    limits: account.limits,
    createdAt: account.createdAt,
    region: account.regions[0],
    overrunPrice: account.billing.overrunPrice,
    isOverrun: account.limits.isOverrun,
    needShopifyApproveForOverrun: account.billing.needShopifyApproveForOverrun,
  };
};

const updateLogin = (loginId, params) => {
  return getCollectionRef(LOGINS_REF).findOneAndUpdate(
    {
      _id: new ObjectID(loginId),
    },
    { $set: params },
  );
};

const getAccountByLoginId = (loginId) => {
  return getCollectionRef(COLL_REF).findOne({
    'users.loginId': new ObjectID(loginId),
  });
};

const getWithAllLogins = async (id) => {
  try {
    let criteria;
    if (ObjectID.isValid(id)) {
      criteria = { _id: new ObjectID(id) };
    } else {
      const databaseId = parseInt(id, 10);
      criteria = { databaseId };
    }
    let acc = await getCollectionRef(COLL_REF).findOne(criteria, {
      projection: {
        createdAt: 1,
        settings: 1,
        subAccounts: 1,
        limits: 1,
        billing: 1,
        databaseId: 1,
        users: 1,
        name: 1,
        type: 1,
        partnerType: 1,
        features: 1,
      },
    });
    if (acc) {
      const users = await getLoginsForAccounts([acc]);
      acc = accountHelper.assignLogins(acc, users);
      if (acc.billing?.package) {
        const isFreemium = planDetails.isFreemiumPackage(acc.billing.package);
        const maxVisitor = isFreemium ? acc.limits.maxPageViews : acc.limits.maxVisitor;

        const remainingVisitor = isFreemium
          ? acc.limits.maxPageViews - acc.limits.pageViews
          : acc.limits.maxVisitor - acc.limits.usedVisitor;

        acc.limits.maxVisitor = maxVisitor;
        acc.limits.remainingVisitor = remainingVisitor;
      }
    }

    return acc;
  } catch (e) {
    console.log('error', e.message, e);
    throw e;
  }
};

const getLoginById = (id) => getCollectionRef(LOGINS_REF).findOne({ _id: new ObjectID(id) });

const getLoginsForAccounts = async (accs) => {
  try {
    const loginsCursor = await getCollectionRef(LOGINS_REF).find(
      { _id: { $in: accountHelper.getAllLoginIds(accs) } },
      { projection: { salt: 0, password: 0 } },
    );
    return loginsCursor.toArray();
  } catch (e) {
    console.error('getLogins error', e.message, e);
  }
  return [];
};

const getRegionsForAccounts = async (accountIds) => {
  const accounts = await getCollectionRef(COLL_REF)
    .find({ databaseId: { $in: accountIds } }, { projection: { databaseId: 1, users: 1 } })
    .toArray();

  const loginIdAccountIdHref = {};
  accounts.forEach((account) => {
    const owner = account.users.find((user) => user.role === 'owner');
    if (owner) {
      loginIdAccountIdHref[owner.loginId.toString()] = account.databaseId;
    }
  });

  const loginIds = Object.keys(loginIdAccountIdHref).map((id) => new ObjectID(id));
  const logins = await getCollectionRef(LOGINS_REF)
    .find({ _id: { $in: loginIds } }, { projection: { region: 1 } })
    .toArray();

  const regionsByAccountIds = {};
  logins.forEach((login) => {
    const databaseId = loginIdAccountIdHref[login._id.toString()];
    regionsByAccountIds[databaseId] = login.region;
  });

  return regionsByAccountIds;
};

const getAccountsForFlexiPayBeforeReset = () => {
  return getCollectionRef(COLL_REF)
    .find(
      {
        'billing.dateExpires': {
          $gte: moment().startOf('day').add(1, 'month').add(14, 'days').toDate(),
        },
        'billing.package': { $nin: ['TRIAL', 'FREE', 'BELSO', 'DEMO'] },
        'limits.isOverrun': true,
        type: { $in: ['agency', 'normal'] },
        $or: [{ 'limits.usedVisitor': { $gt: 0 } }, { 'limits.pageViews': { $gt: 0 } }],
      },
      {
        projection: {
          billing: 1,
          limits: 1,
          databaseId: 1,
          type: 1,
          subAccounts: 1,
        },
      },
    )
    .toArray();
};

const getShopWithShopifyPay = async (databaseId) => {
  const account = await getCollectionRef(COLL_REF).findOne(
    { databaseId: parseInt(databaseId, 10) },
    { projection: { 'settings.shops': 1 } },
  );
  if (!account) return false;

  return account.settings.shops.find((shop) => shop.pay === 1);
};

const getActiveShopifyShopForPayment = async (databaseId) => {
  const account = await getCollectionRef(COLL_REF).findOne(
    { databaseId: parseInt(databaseId, 10) },
    { projection: { 'settings.shops': 1 } },
  );
  if (!account || !account.settings.shops) return false;

  const shopWithPay = account.settings.shops.find((shop) => shop.pay === 1);

  if (shopWithPay) {
    return shopWithPay;
  }
  const shopifyShops = account.settings.shops.filter(
    (shop) => shop.type === 'shopify' && shop.active === 1,
  );
  // if there is only one, return it
  if (shopifyShops.length === 1) {
    return shopifyShops[0];
  }
  if (shopifyShops.length > 1) {
    // if there is more than one, find the one with the oldest creation date and return it
    return shopifyShops.reduce(
      (prev, current) => (prev.date_created < current.date_created ? prev : current),
      shopifyShops[0],
    );
  }
  return false;
};

const activateShopifyPay = (databaseId, shopifyId) => {
  return getCollectionRef(COLL_REF).findOneAndUpdate(
    {
      databaseId: parseInt(databaseId, 10),
      'settings.shops.shopify_id': shopifyId,
    },
    {
      $set: {
        'settings.shops.$.pay': 1,
        'settings.shops.$.active': 1,
      },
    },
  );
};

const updateBillingForShopify = async (
  databaseId,
  productId,
  packageName,
  datePaid,
  dateExpires,
) => {
  const shop = await getActiveShopifyShopForPayment(databaseId);
  const query = { 'settings.shops.shopify_id': shop.shopify_id };
  const update = {
    'billing.datePaid': datePaid,
    'billing.dateExpires': dateExpires,
    'billing.period': 1,
    'settings.shops.$.pay': 1,
  };

  if (packageName) update['billing.package'] = packageName;

  return updateBilling(databaseId, productId, query, update);
};

const setShopifyPay = async (databaseId, value) => {
  if (![0, 1].includes(value)) {
    logger.error(`Invalid shopify pay status when trying to modify: ${value}`);
    return false;
  }

  const shop = await getActiveShopifyShopForPayment(databaseId);

  if (!shop) {
    logger.error(`No shopify shop found for payment for databaseId: ${databaseId}`);
    return false;
  }

  return getCollectionRef(COLL_REF).findOneAndUpdate(
    { databaseId: parseInt(databaseId, 10), 'settings.shops.shopify_id': shop.shopify_id },
    { $set: { 'settings.shops.$.pay': value } },
  );
};

const updatePackage = async (databaseId, toPackageSKU, limitMultiplier = 1) => {
  const product = await productRepository.findBySKuWithLimits(toPackageSKU);

  const productDetails = planDetails.fromString(toPackageSKU);

  const update = {
    'billing.package': productDetails.name.toUpperCase(),
    'billing.period': parseInt(productDetails.period, 10),
    'limits.maxVisitor': product.visitors * limitMultiplier,
    'limits.maxPageViews': product.visitors * limitMultiplier,
    'limits.campaigns': product.campaigns * limitMultiplier,
    'limits.domains': product.domains * limitMultiplier,
  };

  return runUpdate(databaseId, {
    $set: update,
    $unset: {
      'billing.cancelledAt': '',
      'billing.downgradedAt': '',
      'billing.futurePackage': '',
      'settings.downgrade': '',
      'profile.recommendedPlan': '',
    },
  });
};

const deleteCancelAndDowngrade = (databaseId) =>
  runUpdate(databaseId, {
    $unset: {
      'billing.cancelledAt': '',
      'billing.downgradedAt': '',
      'settings.downgrade': '',
    },
  });

const updatePaymentDates = (databaseId, datePaid, dateExpires) =>
  runUpdate(databaseId, {
    $set: { 'billing.datePaid': datePaid, 'billing.dateExpires': dateExpires },
  });

const setGracePeriod = (databaseId, reasonType, _startDate = null) => {
  const startDate = _startDate ?? moment().utc().toDate();
  return runUpdate(databaseId, {
    $set: {
      'billing.gracePeriod': {
        startDate,
        reasonType,
      },
    },
  });
};

const unsetGracePeriod = (databaseId) => {
  return runUpdate(databaseId, {
    $set: {
      'billing.gracePeriod': null,
    },
  });
};

// FIXME: NEED REFACTOR! too much authority for updateBilling to use it outside of the repository
const updateBilling = async (databaseId, productId, query, update) => {
  const productAttributes = {
    campaigns: 'integer_2',
    domains: 'integer_3',
    visitors: 'integer_5',
  };
  let limits = {
    'limits.pageViews': update['limits.pageViews'] || 0,
    'limits.usedVisitor': update['limits.usedVisitor'] || 0,
    'limits.limitReachedAt': null,
    'limits.limit80EmailSent': false,
    'limits.limitReachedEmailSent': false,
  };

  if (productId) {
    const attributes = await productRepository.getAttributesById(productId);
    limits = {
      ...limits,
      'limits.maxVisitor': attributes[productAttributes.visitors],
      'limits.maxPageViews': attributes[productAttributes.visitors],
      'limits.campaigns': attributes[productAttributes.campaigns],
      'limits.domains': attributes[productAttributes.domains],
    };
  }

  update = {
    ...update,
    ...limits,
  };

  try {
    return getCollectionRef(COLL_REF).findOneAndUpdate(
      { databaseId: parseInt(databaseId, 10), ...query },
      { $set: update },
    );
  } catch (e) {
    logger.error(`Billing update error: ${e.message}`, e);
  }
};

const updateBillingWithMultiplier = async (
  databaseId,
  productId,
  { pageViews, usedVisitor, packageName, period, datePaid, dateExpires },
  limitMultiplier = 1,
) => {
  const productAttributes = {
    campaigns: 'integer_2',
    domains: 'integer_3',
    visitors: 'integer_5',
  };
  let limits = {
    'limits.pageViews': pageViews || 0,
    'limits.usedVisitor': usedVisitor || 0,
    'limits.limitReachedAt': null,
    'limits.limit80EmailSent': false,
    'limits.limitReachedEmailSent': false,
  };

  if (productId) {
    const attributes = await productRepository.getAttributesById(productId);
    limits = {
      ...limits,
      'limits.maxVisitor': attributes[productAttributes.visitors] * limitMultiplier,
      'limits.maxPageViews': attributes[productAttributes.visitors] * limitMultiplier,
      'limits.campaigns': attributes[productAttributes.campaigns] * limitMultiplier,
      'limits.domains': attributes[productAttributes.domains] * limitMultiplier,
    };
  }

  const update = {
    'billing.package': packageName,
    'billing.datePaid': datePaid,
    'billing.dateExpires': dateExpires,
    'billing.period': period,
    ...limits,
  };

  try {
    return getCollectionRef(COLL_REF).findOneAndUpdate(
      { databaseId: parseInt(databaseId, 10) },
      { $set: update },
    );
  } catch (e) {
    logger.error(`Billing update error: ${e.message}`, e);
  }
};

const cancelSubscription = (databaseId) => {
  return getCollectionRef(COLL_REF).findOneAndUpdate(
    { databaseId },
    { $set: { 'billing.cancelledAt': new Date() } },
  );
};

const getLocale = async (databaseId) => {
  const account = await getCollectionRef(COLL_REF)
    .aggregate([
      { $match: { databaseId: Number(databaseId) } },
      {
        $lookup: {
          localField: 'users.0.loginId',
          from: 'master_logins',
          foreignField: '_id',
          as: 'logins',
        },
      },
      {
        $project: {
          users: 1,
          logins: { locale: 1 },
        },
      },
    ])
    .toArray();

  return account[0].logins[0].locale;
};

const getRegion = async (databaseId) => {
  const account = await getCollectionRef(COLL_REF)
    .aggregate([
      { $match: { databaseId: Number(databaseId) } },
      {
        $lookup: {
          localField: 'users.0.loginId',
          from: 'master_logins',
          foreignField: '_id',
          as: 'logins',
        },
      },
      {
        $project: {
          users: 1,
          logins: { region: 1 },
        },
      },
    ])
    .toArray();

  return account[0].logins[0].region;
};

const subAccountsFor = (databaseId) => {
  return getCollectionRef(COLL_REF)
    .aggregate([
      { $match: { databaseId } },
      { $unwind: '$subAccounts' },
      {
        $lookup: {
          from: 'master_accounts',
          localField: 'subAccounts',
          foreignField: '_id',
          as: 'subAccount',
        },
      },
      { $unwind: '$subAccount' },
      { $sort: { databaseId: 1 } },
    ])
    .toArray();
};

const resetSubAccountLimitsFor = async (databaseId) => {
  const { subAccounts } = await getCollectionRef(COLL_REF).findOne(
    { databaseId: parseInt(databaseId, 10) },
    { subAccounts: 1 },
  );

  if (subAccounts && subAccounts.length) {
    return getCollectionRef(COLL_REF).update(
      { _id: { $in: subAccounts } },
      { $set: { 'limits.usedVisitor': 0, 'limits.pageViews': 0 } },
      { multi: true },
    );
  }

  return false;
};

const resetLimits = (databaseId) => {
  return getCollectionRef(COLL_REF).updateOne(
    { databaseId: parseInt(databaseId, 10) },
    {
      $set: {
        'limits.usedVisitor': 0,
        'limits.pageViews': 0,
        'limits.limitReachedAt': null,
        'limits.limit80EmailSent': false,
        'limits.limitReachedEmailSent': false,
        'limits.salesPinged': false,
        'limits.limit80SalesPing': false,
      },
    },
  );
};

const resetLimitsForAccounts = (databaseIds) => {
  return getCollectionRef(COLL_REF).update(
    { databaseId: { $in: databaseIds } },
    {
      $set: {
        'limits.usedVisitor': 0,
        'limits.pageViews': 0,
        'limits.limitReachedAt': null,
        'limits.limit80EmailSent': false,
        'limits.limitReachedEmailSent': false,
        'limits.salesPinged': false,
        'limits.limit80SalesPing': false,
      },
    },
    {
      multi: true,
    },
  );
};

const getAccountsForLimitReset = () => {
  return getCollectionRef(COLL_REF)
    .aggregate([
      {
        $match: {
          type: { $in: ['normal', 'agency'] },
          $or: [{ 'limits.usedVisitor': { $gt: 0 } }, { 'limits.pageViews': { $gt: 0 } }],
        },
      },
      {
        $lookup: {
          localField: 'users.0.loginId',
          from: 'master_logins',
          foreignField: '_id',
          as: 'logins',
        },
      },
      {
        $project: {
          billing: 1,
          databaseId: 1,
          type: 1,
          limits: 1,
          subAccounts: 1,
          logins: 1,
          users: 1,
          settings: 1,
        },
      },
    ])
    .toArray();
};

const getAgencyAccountsForUsageStat = () =>
  getCollectionRef(COLL_REF)
    .aggregate([
      {
        $match: {
          type: { $in: ['agency'] },
        },
      },
      {
        $lookup: {
          localField: 'users.0.loginId',
          from: 'master_logins',
          foreignField: '_id',
          as: 'logins',
        },
      },
      {
        $project: {
          billing: 1,
          databaseId: 1,
          type: 1,
          limits: 1,
          subAccounts: 1,
          logins: 1,
          users: 1,
        },
      },
    ])
    .toArray();

const updateMaxVisitor = (_id, maxVisitor) => {
  return getCollectionRef(COLL_REF).updateOne(
    { _id },
    { $set: { 'limits.maxVisitor': maxVisitor } },
  );
};

const updateSettings = async (
  databaseId,
  {
    poweredByDisabled,
    limitOverrun,
    preventSubscribeEmail,
    preventSubscribeStorage,
    canBeManagedByAgency,
    hideSubAccountsTable,
  },
) => {
  const set = {};

  if (typeof poweredByDisabled === 'boolean')
    set['settings.hasPoweredByLinkDisabled'] = poweredByDisabled;
  if (typeof limitOverrun === 'boolean') set['limits.isOverrun'] = limitOverrun;
  if (typeof preventSubscribeStorage === 'boolean')
    set['settings.preventSubscribe.storage'] = preventSubscribeStorage;
  if (typeof preventSubscribeEmail === 'object')
    set['settings.preventSubscribe.emails'] = preventSubscribeEmail;
  if (typeof canBeManagedByAgency === 'boolean')
    set['settings.affiliate.canBeManagedByAgency'] = canBeManagedByAgency;
  if (typeof hideSubAccountsTable === 'boolean')
    set['settings.hideSubAccountsTable'] = hideSubAccountsTable;

  try {
    return await runUpdate(databaseId, {
      $set: set,
    });
  } catch (e) {
    console.log('account settings error', e.message, e);
    throw e;
  }
};

const updateAffiliateLink = (databaseId, affiliateLink) => {
  return getCollectionRef(COLL_REF).updateOne(
    { databaseId, 'settings.general.key': 'affiliate' },
    { $set: { 'settings.general.$.value': affiliateLink } },
  );
};

const runUpdate = async (databaseId, query) => {
  try {
    return await getCollectionRef(COLL_REF).findOneAndUpdate(
      { databaseId: parseInt(databaseId, 10) },
      query,
      { new: true },
    );
  } catch (e) {
    console.log('update error', e.message, e);
    throw e;
  }
};

const getWithLogins = (databaseId) => {
  return getCollectionRef(COLL_REF)
    .aggregate([
      { $match: { databaseId } },
      {
        $lookup: {
          localField: 'users.0.loginId',
          from: 'master_logins',
          foreignField: '_id',
          as: 'logins',
        },
      },
    ])
    .toArray();
};

const getWithLoginsAndOwner = async (databaseId) => {
  databaseId = parseInt(databaseId, 10);
  const accountArray = await getWithLogins(databaseId);
  const account = accountArray[0] ?? null;
  if (!account?.users?.length) {
    return false;
  }

  const users = await getLoginsForAccounts(accountArray);
  accountHelper.assignLogins(account, users);
  return account;
};

const payingUsers = () => {
  return getCollectionRef(COLL_REF)
    .find({
      'billing.dateExpires': { $gte: new Date() },
    })
    .toArray();
};

const convertToAgency = async (databaseId) => {
  const account = await get(databaseId);
  const login = await getCollectionRef(LOGINS_REF).findOne({
    _id: account.users[0].loginId,
  });
  const { value: next } = await getCollectionRef(COUNTER_REF).findOneAndUpdate(
    { name: 'accountId' },
    { $inc: { value: 1 } },
  );

  const newAgencyAccount = _.cloneDeep(account);
  delete newAgencyAccount._id;
  newAgencyAccount.databaseId = next.value;
  newAgencyAccount.type = ACCOUNT_TYPES.AGENCY;
  newAgencyAccount.partnerType = PARTNER_TYPES.RESELLER;
  newAgencyAccount.subAccounts = [account._id];
  newAgencyAccount.users = [{ loginId: login._id, role: 'owner' }];
  newAgencyAccount.name = login.email;
  newAgencyAccount.features = account.features;
  newAgencyAccount.settings.hasPoweredByLinkDisabled = true;
  // Copy max page view to max visitor if not set (case: Converting normal FREE account to agency)
  if (!newAgencyAccount.limits.maxVisitor) {
    newAgencyAccount.limits.maxVisitor = newAgencyAccount.limits.maxPageViews;
  }

  account.type = ACCOUNT_TYPES.SUB;
  account.partnerType = PARTNER_TYPES.SUBACCOUNT;
  account.billing = {};
  account.limits = { maxVisitor: null, usedVisitor: 0 };
  account.users = [{ loginId: login._id, role: 'owner' }];

  await getCollectionRef(COLL_REF).findOneAndUpdate({ databaseId }, { $set: account });
  await getCollectionRef(COLL_REF).insert(newAgencyAccount);

  await userActivityLogRepository.update(databaseId, { database_id: next.value });

  return next.value;
};

const hasDowngradeSetting = async (databaseId) => {
  const count = await getCollectionRef(COLL_REF).count(
    {
      databaseId,
      'billing.downgradedAt': { $exists: true, $ne: null },
      'settings.downgrade.pending': false,
      $or: [
        { 'settings.downgrade.needShopifyApprove': { $exists: false } },
        { 'settings.downgrade.needShopifyApprove': false },
      ],
    },
    { databaseId: 1, 'settings.downgrade': 1, billing: 1 },
  );
  return !!count;
};

const clearShopifyPay = (databaseId) => {
  return getCollectionRef(COLL_REF).findOneAndUpdate(
    { databaseId: parseInt(databaseId, 10), 'settings.shops.type': 'shopify' },
    { $set: { 'settings.shops.$[].pay': 0 } },
  );
};

const getOwner = (id) => {
  return getCollectionRef(COLL_REF).findOne({
    subAccounts: new ObjectID(id),
    $or: [{ deleted: { $exists: false } }, { deleted: false }],
  });
};

const getSubAccounts = (account) => {
  return getCollectionRef(COLL_REF)
    .aggregate([
      { $match: { _id: { $in: account.subAccounts }, type: 'sub' } },
      { $project: { name: 1, databaseId: 1, features: 1 } },
    ])
    .toArray();
};

const getSubAccountDatabaseIds = (subAccounts) => {
  return getCollectionRef(COLL_REF)
    .aggregate([
      { $match: { _id: { $in: subAccounts }, type: 'sub' } },
      { $project: { databaseId: 1 } },
      { $sort: { databaseId: 1 } },
    ])
    .toArray();
};

const setOverrun = (databaseId, value) =>
  getCollectionRef(COLL_REF).update(
    { databaseId: parseInt(databaseId, 10) },
    { $set: { 'limits.isOverrun': value } },
  );

const unsetOverrunApproving = (databaseId) =>
  runUpdate(databaseId, {
    $unset: {
      'limits.needShopifyApproveForOverrun': '',
    },
  });

const clearRecommendedPlan = (databaseId) =>
  runUpdate(databaseId, {
    $unset: {
      'profile.recommendedPlan': '',
    },
  });

const isAccountExpired = async (databaseId) => {
  const {
    billing: { dateExpires },
  } = await getCollectionRef(COLL_REF).findOne(
    { databaseId: parseInt(databaseId, 10) },
    { 'billing.dateExpires': 1 },
  );

  return moment(dateExpires)
    .subtract(14, 'days')
    .startOf('day')
    .isSameOrBefore(moment().startOf('day'));
};

const deleteByIds = async (objectIds) => {
  if (objectIds?.length === 0) return;

  return getCollectionRef(COLL_REF).deleteMany({ _id: { $in: objectIds } });
};

module.exports = {
  expiringAccounts,
  runUpdate,
  payingUsers,
  resetSubAccountLimitsFor,
  updateMaxVisitor,
  list,
  get,
  getByEmail,
  count,
  resetLimitsForAll,
  getActiveShopifyShopForPayment,
  updateBillingForShopify,
  updatePackage,
  updateBilling,
  updateBillingWithMultiplier,
  cancelSubscription,
  getLocale,
  getRegion,
  getWithAllLogins,
  updateLogin,
  updateSettings,
  subAccountsFor,
  getShopWithShopifyPay,
  getAccountWithRegion,
  getWithLogins,
  getWithLoginsAndOwner,
  getRegionsForAccounts,
  convertToAgency,
  updateAffiliateLink,
  getAccountsForFlexiPayBeforeReset,
  activateShopifyPay,
  hasDowngradeSetting,
  getAccountByLoginId,
  clearShopifyPay,
  getOwner,
  getLoginsForAccounts,
  getSubAccounts,
  getSubAccountDatabaseIds,
  setOverrun,
  unsetOverrunApproving,
  clearRecommendedPlan,
  updatePaymentDates,
  setGracePeriod,
  unsetGracePeriod,
  resetLimits,
  setShopifyPay,
  resetLimitsForAccounts,
  getAccountsForLimitReset,
  getAgencyAccountsForUsageStat,
  isAccountExpired,
  deleteCancelAndDowngrade,
  deleteByIds,
  getByObjectId,
  getLoginById,
};
