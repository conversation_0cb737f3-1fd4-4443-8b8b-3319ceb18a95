const moment = require('moment');
const { planModelFor, discountPlanModel, couponCodeFor } = require('../plans');
const getBaseDeal = require('./baseDeal');

const getDeal = (account, login, locale) => {
  let couponCode = 'HU-BLACKFRIDAY-2021-25';
  locale = 'hu';

  if (
    (login && login.region !== 'Hungary') || // This deal is only available for Hungarians
    (account && account.type !== 'normal') // only for normal users
  ) {
    return null;
  }

  if (
    account &&
    /(BRONZE|SILVER|GOLD|PLATINUM|DIAMOND|ENTERPRISE)/i.test(account.billing.package)
  ) {
    const dateExpires = moment(account.billing.dateExpires);
    const datePaid = dateExpires.subtract(2, 'weeks');

    if (datePaid.isValid() && moment().isBefore(datePaid)) {
      // user has currently active paid plan
      return null;
    }
  }

  if (login) {
    couponCode = couponCodeFor(login.email, couponCode);
  }

  const discount = 25; // 9+3
  const planModel = discountPlanModel(planModelFor(account, locale), discount);
  return {
    ...getBaseDeal(locale, planModel),
    discount,
    deal: 'hu-blackfriday-2021-10',
    couponCode,
    currency: 'HUF',
    paymentMethods: [
      {
        name: 'credit-card',
        kind: 'dropin',
        surcharge: 0,
        currency: 'HUF',
        default: true,
      },
      { name: 'bank-transfer', surcharge: 390, currency: 'HUF' },
    ],
    zapier: 'https://hooks.zapier.com/hooks/catch/107350/b6xmgcw/',
  };
};

module.exports = getDeal;
