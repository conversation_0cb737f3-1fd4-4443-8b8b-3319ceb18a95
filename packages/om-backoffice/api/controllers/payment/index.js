const moment = require('moment');

const { getOrderProductName } = require('@om/payment/src/helpers/planDetails');
const { COUPON_ACTION_ORIGIN } = require('@om/payment/src/helpers/couponEnums');

const BraintreeAdapter = require('../../integrations/braintree');
const OptimonkAdapter = require('../../integrations/optimonk');

const paymentService = require('../../lib/services/paymentService');
const billingService = require('../../lib/services/billingService');
const customerRepository = require('../../lib/repositories/customerRepository');
const braintreeCancelRepository = require('../../lib/repositories/braintreeCancelRepository');
const keepSubscriptionRepository = require('../../lib/repositories/keepSubscriptionRepository');
const accountRepository = require('../../lib/repositories/accountRepository');
const addressRepository = require('../../lib/repositories/addressRepository');

const shopifyPaymentService = require('../../lib/services/shopifyPaymentService');
const visitorLimitResetService = require('../../lib/services/visitorLimitReset');
const flexiPayService = require('../../lib/services/flexiPayService');
const couponService = require('../../lib/services/couponService');
const downgradeService = require('../../lib/services/downgradeService');

const { isFreemiumSKU } = require('../../lib/helpers/planDetails');
const usageReport = require('../../lib/services/subUserUsageReporter');
const dateCalculatorHelper = require('../../lib/helpers/dateCalculator');
const { calculateUpgrade } = require('../../lib/helpers/packageCalculator');
const handleRedisLimit = require('../../lib/services/redisLimitCount');

const logger = require('../../lib/helpers/infrastructure/logger').child({
  service: 'payment',
});

const optimonk = new OptimonkAdapter();
const bt = new BraintreeAdapter();

const getInactivePaymentsForAccount = async ({ params: { id } }, res) => {
  const salesCustomer = await customerRepository.getByOptimonkId(id);
  const account = await accountRepository.get(id);
  if (!salesCustomer || !salesCustomer.braintree_id) {
    return [];
  }

  const inactivePaymentMethods = await bt.getInactivePaymentMethods(
    salesCustomer.braintree_id,
    account,
  );

  res.send(inactivePaymentMethods);
};

const deletePaymentMethods = async ({ params: { token } }, res) => {
  try {
    const result = await bt.deletePaymentMethod(token);
    return res.send(result);
  } catch (e) {
    logger.error('Error during payment method deletion!', e);
  }

  return res.send({});
};

const createOrder = async (
  {
    app: {
      locals: { accounts: accountRepo },
    },
    params: { dbId },
    body: {
      method,
      packageName,
      otherProducts,
      period,
      paymentNonce,
      couponCode,
      address,
      flexiPay, // total in HUF
    },
  },
  res,
) => {
  if (otherProducts && typeof otherProducts !== 'object') otherProducts = JSON.parse(otherProducts);

  if (flexiPay) {
    if (!otherProducts) {
      otherProducts = [];
    }
    otherProducts.push(flexiPayService.getAsProductFromTotal(flexiPay));
  }

  const result = await paymentService.createOrder({
    dbId,
    accountRepo,
    method,
    packageName,
    otherProducts,
    period,
    paymentNonce,
    couponCode,
    address,
  });

  if (result && result.success) {
    return res.json({ success: true, message: 'Successful transaction!' });
  }

  return res.json({
    success: false,
    message: result.message,
    response: result.response || null,
  });
};

const _cancelLog = ({ databaseId, method, isFreemium, isViaShopifyUninstall = false }) => {
  logger.info({
    type: 'cancel',
    action: 'Succesful subscription cancelation',
    accountId: databaseId,
    method,
    isFreemium,
    isViaShopifyUninstall,
  });
};

const cancelSubscription = async (req, res) => {
  const { userId } = req.params;
  const { reason } = req.body;
  const databaseId = Number(userId);
  const account = await accountRepository.getWithLoginsAndOwner(databaseId);
  const customer = await customerRepository.getByOptimonkId(databaseId);
  const method = customer.payment_method;
  const currentPackageSku = customer.current_package_sku;
  const isFreemium = isFreemiumSKU(currentPackageSku);

  if (method === 'shopify' && isFreemium) {
    const shopifyCancelResult = await shopifyPaymentService.cancelShopifySubscription(account, {
      isRequiredSuccessfulFlexiPayPayoff: true,
    });

    return res.send(shopifyCancelResult);
  }

  if (method === 'braintree' || method === 'braintreepaypal') {
    if (!isFreemium) {
      await accountRepository.deleteCancelAndDowngrade(databaseId);
      await accountRepository.cancelSubscription(databaseId);
      await paymentService.unsetVisitorOverrunSettings(account);
      await couponService.handleDeactivationForAccount(databaseId, COUPON_ACTION_ORIGIN.ADMIN);
    }
    await braintreeCancelRepository.insert(customer.customer_id, reason);
    await accountRepository.clearRecommendedPlan(userId);

    _cancelLog({ databaseId, method, isFreemium });
    res.send({ success: true });
    return;
  }

  logger.error({
    type: 'cancel',
    action: 'Unhandled cancel case',
    accountId: databaseId,
    method,
    isFreemium,
  });

  res.send({ success: false });
};

const handleShopifyUninstall = async (req, res) => {
  const { userId } = req.params;
  const databaseId = Number(userId);
  let account = await accountRepository.getWithLoginsAndOwner(databaseId);
  const customer = await customerRepository.getByOptimonkId(databaseId);
  const method = customer.payment_method;
  const currentPackageSku = customer.current_package_sku;
  const isFreemium = isFreemiumSKU(currentPackageSku);
  const isViaShopifyUninstall = true;

  if (isFreemium) {
    const downgradeSettingsSuccess = await downgradeService.setDowngradeSettings(databaseId, {
      plan: 'FREE',
      period: 1,
      activeDomainIds: null,
      hasFunctionalDowngrade: true,
      needShopifyApprove: false,
      skipDisableLostFeatures: true,
    });
    if (!downgradeSettingsSuccess) {
      logger.error({
        type: 'cancel',
        errorMessage: 'Caught error during set downgrade settings',
        accountId: databaseId,
        method: 'shopify',
        isFreemium,
        isViaShopifyUninstall,
      });
      return res.send({ success: false, reason: 'unsuccessful-set-downgrade-settings' });
    }

    account = await accountRepository.getWithLoginsAndOwner(databaseId);
  }

  const shopifyCancelResult = await shopifyPaymentService.cancelShopifySubscription(account, {
    isFreemium,
    isRequiredSuccessfulFlexiPayPayoff: false,
    isViaShopifyUninstall,
  });

  _cancelLog({ databaseId, method, isFreemium, isViaShopifyUninstall });

  return res.send(shopifyCancelResult);
};

const keepSubscription = async (req, res) => {
  const { userId } = req.params;
  const { reason, solution } = req.body;
  const { customer_id: customerId, email } = await customerRepository.getByOptimonkId(userId);

  await keepSubscriptionRepository.create({
    customer_id: customerId,
    email,
    reason,
    solution,
  });

  const logs = await keepSubscriptionRepository.getByCustomerId(customerId);

  optimonk.sendKeepSubscriptionEmail({
    customerId,
    email,
    reason,
    solution,
    nrOfRequested: logs.length,
  });

  res.send({ success: true });
};

const getBilling = async (req, res) => {
  const { userId } = req.params;
  const { planValidation, getShopifyPlan } = req.query;
  const billingResult = await billingService.getBillingWithTryCatch(
    userId,
    planValidation === 'true',
    getShopifyPlan === 'true',
  );

  if (!billingResult) return res.send({ success: false });

  const { paymentMethod, billing, paymentData, paymentRecord } = billingResult;

  res.send({
    success: true,
    paymentMethod,
    billing,
    paymentData,
    paymentRecord,
  });
};

const updateBillingAddress = async (req, res) => {
  const { userId } = req.params;
  const customer = await customerRepository.getByOptimonkId(userId);
  await addressRepository.upsert(customer.customer_id, req.body);

  res.send({ success: true });
};

const updatePayment = async (req, res) => {
  const { method, paymentNonce, optimonkId } = req.body;
  const customer = await customerRepository.getByOptimonkId(optimonkId);
  await customerRepository.updatePaymentMethod(optimonkId, method);
  if (method.indexOf('braintree') === 0) {
    if (!customer.braintree_id || customer.braintree_id === '0') {
      const result = await bt.createCustomer(
        {
          customerId: customer.customer_id,
          firstName: customer.firstname,
          lastName: customer.lastname,
          email: customer.email,
        },
        paymentNonce,
      );

      if (!result.success) {
        res.send({ success: false });
        return;
      }

      const braintreeId = result.response.customer.id;

      await customerRepository.updateBraintreeId({
        customerId: customer.customer_id,
        braintreeId,
      });
    } else {
      const { response } = await bt.addPaymentMethod(customer.braintree_id, paymentNonce);

      if (!response.success) {
        res.send({ success: false });
        return;
      }

      await bt.makePaymentTokenDefault(customer.customer_id, response.paymentMethod.token);
    }
  }

  res.send({ success: true });
};

const getBraintreeCancelReport = async (req, res) => {
  const report = await braintreeCancelRepository.list();
  res.send({ success: true, report });
};

const flexiPayBeforeReset = async (
  {
    app: {
      locals: { accounts: accountRepo },
    },
  },
  res,
) => {
  const accounts = await paymentService.getAccountsForFlexiPayBeforeReset();
  const databaseIds = accounts.map((account) => {
    return account.databaseId;
  });
  const paymentMethods = await customerRepository.getPaymentMethodsBy(databaseIds);
  const transactions = paymentService.getFlexiPayTransactionsFor(accounts, paymentMethods);
  const transactionsWithResults = await paymentService.createFlexiPayOrders(
    accountRepo,
    transactions,
    logger,
  );

  const result = {
    numberOfAccountForReset: accounts.length,
    numberOfAccountForFlexiPay: transactions.length,
    numberOfSuccessPayment: transactions.filter((t) => t.result.success === true).length,
    numberOfFailedPayment: transactions.filter((t) => t.result.success !== true).length,
    transactionsWithResults,
  };

  logger.info(`FlexiPay before limit reset result`, result);

  return res.json({ success: true, result });
};

const resetLimits = async (_, res) => {
  logger.info(`[limit-reset-cron] New payment limit reset - Start`);

  const accounts = await accountRepository.getAccountsForLimitReset();

  const accountsToReset = accounts.filter(({ billing: { dateExpires } }) =>
    visitorLimitResetService.shouldResetLimits(dateExpires),
  );

  if (!accountsToReset.length) {
    logger.info(`[limit-reset-cron] New payment limit reset - No accounts to reset `);
    return res.json({ success: true, message: 'No accounts to reset' });
  }

  const idListToReset = accountsToReset.map(({ databaseId }) => databaseId);

  logger.info(`[limit-reset-cron] New payment limit reset - Accounts`, idListToReset);

  let notifierSendingSuccess;
  try {
    await optimonk.sendLimitNotifiers(idListToReset);
    logger.info(`[limit-reset-cron] Limit notifications sent`);
    notifierSendingSuccess = true;
  } catch (e) {
    logger.error('[limit-reset-cron] Error during new payment limit reset notifier sending!', {
      accountsForReset: idListToReset,
    });
    notifierSendingSuccess = false;
  }

  const {
    success: resetSuccess,
    error: resetError,
    agencyUsageReports,
  } = await visitorLimitResetService.resetLimitsForAccounts(accountsToReset);
  logger.info(`[limit-reset-cron] Successful limit reset`);

  if (!resetSuccess) {
    logger.error('[limit-reset-cron] Error during new payment limit reset!', {
      accountsForReset: idListToReset,
      error: resetError,
    });
  }

  for (const account of accountsToReset) {
    await handleRedisLimit(account);
  }

  const result = {
    numberOfAccountForReset: accountsToReset.length,
    accountsForReset: idListToReset,
    notifierSendingSuccess,
    resetSuccess,
    agencyUsageReports,
  };

  logger.info(`[limit-reset-cron] New payment limit reset - End`, result);

  res.json({ success: true, result });
};

const generateUsageReport = async ({ params: { databaseId } }, res) => {
  const statistics = await usageReport.createActualUsageReport(databaseId);
  return res.send({ success: true, statistics });
};

const createAgencyUsageReportToTemp = async ({ query: { databaseId } }, res) => {
  let accounts = await accountRepository.getAgencyAccountsForUsageStat();
  if (databaseId) {
    accounts = accounts.filter(
      (account) => parseInt(account.databaseId, 10) === parseInt(databaseId, 10),
    );
  }
  const result = await usageReport.saveCurrentUsageReportToTemp(accounts);
  return res.send(result);
};

const sendAgencyUsageReportFromTemp = async ({ query: { databaseId, day, onlyUs } }, res) => {
  const result = await usageReport.sendUsageReportFromTemp(day, parseInt(databaseId, 10), !!onlyUs);
  return res.send(result);
};

// Package change info for BO fontend order creation
const getPackageChangeDetails = async (
  { params: { userId }, query: { toPackageSKU, eventDate, toPackageQuantity = 1, coupon = null } },
  response,
) => {
  try {
    const [account, customer] = await Promise.all([
      accountRepository.get(userId),
      customerRepository.getByOptimonkId(userId),
    ]);

    const fromPackageSKU = customer.current_package_sku;
    const dateExpires = account.billing.dateExpires;
    const periodFrom = eventDate ? moment.utc(eventDate) : moment.utc();

    // Calculate upgrade or full price
    const price = await calculateUpgrade(
      fromPackageSKU,
      toPackageSKU,
      dateExpires,
      periodFrom,
      toPackageQuantity,
    );

    const { expiresDate: newExpire } = dateCalculatorHelper.calculatePaymentDates({
      eventDate: periodFrom,
      currentSku: fromPackageSKU,
      toPackageSku: toPackageSKU,
      currentExpires: dateExpires,
      toPackageQuantity,
      coupon,
    });
    const periodTill = moment.utc(newExpire).subtract(14, 'days');

    response.send({
      error: null,
      price,
      periodFrom: periodFrom.toDate(),
      periodTill: periodTill.toDate(),
      orderProductName: getOrderProductName({
        sku: toPackageSKU,
        from: periodFrom,
        till: periodTill,
        quantity: toPackageQuantity,
      }),
    });
  } catch (e) {
    console.error({
      type: 'calculatePackageUpgradePrice',
      message: e.message,
      databaseId: userId,
      toPackageSKU,
      eventDate,
    });
    response.status(500).send({ error: 'Something broke!', result: 0 });
  }
};

module.exports = {
  getInactivePaymentsForAccount,
  deletePaymentMethods,
  createOrder,
  updateBillingAddress,
  cancelSubscription,
  handleShopifyUninstall,
  keepSubscription,
  getBilling,
  updatePayment,
  getBraintreeCancelReport,
  flexiPayBeforeReset,
  resetLimits,
  generateUsageReport,
  createAgencyUsageReportToTemp,
  sendAgencyUsageReportFromTemp,
  getPackageChangeDetails,
};
