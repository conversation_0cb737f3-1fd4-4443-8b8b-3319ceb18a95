const moment = require('moment');

const logger = require('../../../lib/helpers/infrastructure/logger').child({
  service: 'limit-usage-report',
});

// LIVE config
const COLLCETION_REFS = {
  accounts: 'accounts',
  limit_resets: 'limit_resets',
};

const REPORT_TABLE = 'limit_usage_report';

const getAccounts = (accounts, filteredPlans) =>
  accounts
    .find(
      {
        'billing.package': { $in: filteredPlans },
        'billing.dateExpires': { $gt: new Date() },
      },
      {
        projection: {
          databaseId: 1,
          'billing.package': 1,
          'limits.maxVisitor': 1,
          'limits.maxPageViews': 1,
        },
      },
    )
    .toArray();

const getLastLimitResets = (limitResets, databaseIds) =>
  limitResets
    .aggregate([
      {
        $match: {
          databaseId: { $in: databaseIds },
        },
      },
      {
        $sort: {
          databaseId: 1,
          date: -1,
        },
      },
      {
        $group: {
          _id: '$databaseId',
          latestEntry: { $first: '$$ROOT' },
        },
      },
      {
        $replaceWith: '$latestEntry',
      },
      {
        $project: {
          databaseId: 1,
          date: 1,
          'billing.package': 1,
          'limits.maxVisitor': 1,
          'limits.maxPageViews': 1,
          'limits.usedVisitor': 1,
          'limits.pageViews': 1,
        },
      },
    ])
    .toArray();

const limitUsageReport = async (req, res) => {
  const region = req.query?.region === 'en' ? 'en' : 'hu';

  try {
    logger.info('start');

    res.send({ start: true });

    const filteredPlans =
      region === 'en'
        ? [/ESSENTIAL/, /GROWTH/, /PREMIUM/]
        : ['BRONZE', 'SILVER', 'GOLD', 'PLATINUM', 'DIAMOND'];

    const _accounts = await getAccounts(req.app.locals[COLLCETION_REFS.accounts], filteredPlans);
    const accounts = _accounts.map((a) => {
      return {
        databaseId: a.databaseId,
        plan: a.billing.package,
      };
    });
    const databaseIds = accounts.map((a) => a.databaseId);

    const _lastResets = await getLastLimitResets(
      req.app.locals[COLLCETION_REFS.limit_resets],
      databaseIds,
    );
    const lastResets = _lastResets
      .map((a) => {
        return {
          databaseId: a.databaseId,
          plan: a.billing.package,
          mUV: a.limits.maxVisitor,
          mPV: a.limits.maxPageViews,
          uUV: a.limits.usedVisitor,
          uPV: a.limits.pageViews,
          rDate: a.date,
        };
      })
      .filter((r) => {
        return r.plan === accounts.find((a) => a.databaseId === r.databaseId)?.plan ?? '';
      });

    const { createConnection } = require('../../helpers/salesMysql');
    const DB_NAME = process.env.MYSQL_DATABASE || 'optimonksales';
    const connection = await createConnection(DB_NAME);

    const insertData = lastResets.map((a) => {
      return {
        region,
        databaseId: a.databaseId,
        plan: a.plan,
        maxUV: a.mUV,
        maxPV: a.mPV,
        usedUV: a.uUV,
        usedPV: a.uPV,
        resetDate: moment.utc(a.rDate).format('YYYY-MM-DD'),
      };
    });

    logger.info({ length: insertData.length, firstItem: insertData?.[0] ?? null });

    if (!insertData.length) {
      logger.info('end');
      return;
    }

    const columns = Object.keys(insertData[0]);

    const valuesList = insertData.map((data) => {
      return `(${columns
        .map((col) => {
          const value = data[col];
          if (typeof value === 'string') {
            return `'${value.replace(/['"`]/g, '')}'`;
          }
          return value;
        })
        .join(', ')})`;
    });

    const query = `INSERT INTO ${REPORT_TABLE} (${columns.join(', ')}) VALUES ${valuesList.join(
      ', ',
    )}`;

    await connection.query(query);
  } catch (err) {
    logger.error({
      message: err.message,
      stack: err.stack,
    });
  }

  logger.info('end');
};

module.exports = {
  limitUsageReport,
};
