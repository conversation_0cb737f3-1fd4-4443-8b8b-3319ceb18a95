const { Executor } = require('@om/workflow/src/executor');
const { logger } = require('@om/logger');
const axiosRetry = require('axios-retry').default;

const { Storage, IdempotencyStrategy } = require('@google-cloud/storage');
const axios = require('axios');
const { stripLeadingSlash } = require('@om/common');
const { getPipeline } = require('./util');

axiosRetry(axios, { retries: 3 });
class NonRetryableError extends Error {
  constructor(message) {
    super(message);
    this.code = 'NON_RETRYABLE_ERROR';
  }
}
class RetryableError extends Error {}

function isRetryableError(error) {
  return (
    error.code !== 'ECONNABORTED' &&
    error.code !== 'ETIMEDOUT' &&
    (!error.response || (error.response.status >= 500 && error.response.status <= 599))
  );
}

const assetDownloaderExecutor = ({ projectId }) => {
  const storage = new Storage({
    projectId,
    retryOptions: {
      autoRetry: true,
      idempotencyStrategy: IdempotencyStrategy.RetryAlways,
      maxRetries: 3,
      retryableErrorFn: () => true,
    },
  });

  return Executor(async (step, params) => {
    try {
      axiosRetry(axios, { retries: 3 });

      const { targetFileUrl, url } = params;
      const targetUrl = new URL(targetFileUrl);
      const targetFile = storage
        .bucket(targetUrl.hostname)
        .file(stripLeadingSlash(targetUrl.pathname));
      const targetWriteStream = targetFile.createWriteStream({
        highWaterMark: 16 * 1024,
      });

      const response = await axios({
        method: 'get',
        url,
        responseType: 'stream',
      });
      await getPipeline()(response.data, targetWriteStream);

      step.nextStep.done({ url: targetFileUrl });
    } catch (error) {
      logger.error(
        { trace: step.flowId, data: { step }, error },
        'Error occurred during file download/upload',
      );

      if (isRetryableError(error)) {
        throw new RetryableError('Retryable error: Failed to download/upload file');
      } else {
        step.setRetry(false);
        const error = new NonRetryableError('NonRetryable error: Failed to download/upload file');

        step.nextStep.failed({
          error,
        });
      }
    }
  });
};

module.exports = { assetDownloaderExecutor, NonRetryableError, RetryableError };
