const { ObjectId } = require('mongodb');

const SPR_COLLECTION_NAME = 'user_recommendations';
const SPR_DATASET_NAME = 'smart_recommendation';

const setEmbeddingGenerationError = async ({ collection, databaseId, domainId, error }) => {
  const filter = {
    accountId: databaseId,
    domainId: new ObjectId(domainId),
  };
  const updateDoc = {
    $set: {
      'textEmbeddingStatus.finishedAt': new Date(),
      'textEmbeddingStatus.error': {
        code: error.code,
        message: error.message,
      },
    },
  };
  return collection.updateOne(filter, updateDoc);
};

const sleep = (ms) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

module.exports = { SPR_COLLECTION_NAME, SPR_DATASET_NAME, setEmbeddingGenerationError, sleep };
