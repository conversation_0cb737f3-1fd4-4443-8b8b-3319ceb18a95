version: '3.3'

services:
  om-fakeclient:
    image: ${IMG_TAG}
    ports:
      - "9100:9100"
    deploy:
      replicas: 3
      restart_policy:
        condition: on-failure
    hostname: om-fakeclient
    logging:
      driver: fluentd
      options:
        fluentd-address: *************:24224
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9100/health"]
      interval: 4s
      timeout: 5s
      retries: 12
    env_file:
      - environment.env
    #healthcheck:
      #test: ["CMD", "curl", "-f", "http://localhost:9100/"]
      #interval: 4s
      #timeout: 5s
      #retries: 12
