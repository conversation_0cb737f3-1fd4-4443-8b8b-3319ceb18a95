const dayjs = require('dayjs');
const { Service } = require('../test/service');

describe('routes/leads', () => {
  beforeAll(async () => {
    this.service = await Service.createServiceInstance();
    this.service.instance.config.endpoints.leads.pagination = 10;
    this.leadsApi = this.service.generateRequestHelper('leads', {
      headers: {
        'x-api-key': this.service.apiKey,
      },
    });
  });

  afterAll(async () => {
    await this.service.stop();
  });

  test('Check basic without parameter', async () => {
    const response = await this.leadsApi.get('/');
    const body = JSON.parse(response.body);
    expect(response.statusCode).toEqual(200);
    expect(body.total).toEqual(20);
    expect(body.currentPage).toEqual(1);
    expect(body.totalPages).toEqual(2);
    expect(body.leads.length).toEqual(10); // based on the current seed
  });

  test('Query last day', async () => {
    const response = await this.leadsApi.get('/', { query: { interval: 'daily' } });
    const body = JSON.parse(response.body);
    expect(response.statusCode).toEqual(200);
    expect(body.total).toEqual(2);
    expect(body.currentPage).toEqual(1);
    expect(body.totalPages).toEqual(1);
    expect(body.leads.length).toEqual(2); // based on the current seed
  });

  test('Query last week', async () => {
    const response = await this.leadsApi.get('/', { query: { interval: 'weekly' } });
    const body = JSON.parse(response.body);
    expect(response.statusCode).toEqual(200);
    expect(body.total).toEqual(20);
    expect(body.currentPage).toEqual(1);
    expect(body.totalPages).toEqual(2);
    expect(body.leads.length).toEqual(10); // based on the current seed
  });

  test('Query last month', async () => {
    const response = await this.leadsApi.get('/', { query: { interval: 'monthly' } });
    const body = JSON.parse(response.body);
    expect(response.statusCode).toEqual(200);
    expect(body.total).toEqual(20);
    expect(body.currentPage).toEqual(1);
    expect(body.totalPages).toEqual(2);
    expect(body.leads.length).toEqual(10); // based on the current seed
  });

  test('Query last year', async () => {
    const response = await this.leadsApi.get('/', { query: { interval: 'yearly' } });
    const body = JSON.parse(response.body);
    expect(response.statusCode).toEqual(200);
    expect(body.total).toEqual(20);
    expect(body.currentPage).toEqual(1);
    expect(body.totalPages).toEqual(2);
    expect(body.leads.length).toEqual(10); // based on the current seed
  });

  test('Query custom interval without from or to', async () => {
    const response = await this.leadsApi.get('/', { query: { interval: 'custom' } });
    expect(response.statusCode).toEqual(400);
  });

  test('Query custom interval without from', async () => {
    const response = await this.leadsApi.get('/', {
      query: { interval: 'custom', to: new Date() },
    });
    expect(response.statusCode).toEqual(400);
  });

  test('Query custom interval without to', async () => {
    const response = await this.leadsApi.get('/', {
      query: { interval: 'custom', from: new Date() },
    });
    expect(response.statusCode).toEqual(400);
  });

  test('Query custom interval with bad from date', async () => {
    const response = await this.leadsApi.get('/', {
      query: { interval: 'custom', from: 'notadate', to: new Date() },
    });
    expect(response.statusCode).toEqual(400);
  });

  test('Query custom interval with bad to date', async () => {
    const response = await this.leadsApi.get('/', {
      query: {
        interval: 'custom',
        to: 'notadate',
        from: new Date(),
      },
    });
    expect(response.statusCode).toEqual(400);
  });

  test('Query custom interval with proper values', async () => {
    const response = await this.leadsApi.get('/', {
      query: {
        interval: 'custom',
        to: new Date(),
        from: dayjs().subtract(1, 'day').subtract(1, 'hour').toDate(),
      },
    });
    const body = JSON.parse(response.body);
    expect(response.statusCode).toEqual(200);
    expect(body.total).toEqual(2);
    expect(body.currentPage).toEqual(1);
    expect(body.totalPages).toEqual(1);
    expect(body.leads.length).toEqual(2); // based on the current seed
  });
});
