build public-api:
  extends: .build
  variables:
    PACKAGE_NAME: om-public-api

test public-api:
  needs: [build public-api]
  extends: .test
  variables:
    PACKAGE_NAME: om-public-api
  artifacts:
    when: always
    reports:
      coverage_report:
        coverage_format: cobertura
        path: packages/om-public-api/coverage/cobertura-coverage.xml
      junit: packages/om-public-api/coverage/junit.xml
    paths:
      - packages/om-public-api/coverage
