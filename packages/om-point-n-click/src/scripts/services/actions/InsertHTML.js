import { ChangeBuilder } from '@om/change-builder';
import { DOM } from './helper/DOM';
import { generateRandomString } from '../../helper';
import { OUTLINE_SHOW_CLASS } from '../../constants';
import TYPES from './helper/types';
import BaseAction from './BaseAction';

export default class InsertHTML extends BaseAction {
  static replaceExistingElement(change) {
    DOM.removeScriptTags(change.id);
    DOM.removeStyleTags(change.id);

    const insertedElement = DOM.getElementById(change.id);
    InsertHTML._handleInsertHTML(change, insertedElement, null, { edit: true });
  }

  static applyChange({ change }) {
    DOM.removeElementById(change.id);
    DOM.removeScriptTags(change.id);
    DOM.removeStyleTags(change.id);

    const element = DOM.getElement(change.selector);
    if (element) {
      InsertHTML._handleInsertHTML(change, element, change.position, { edit: false });
    }

    return change;
  }

  static _handleInsertHTML(change, element, position, { edit = false }) {
    DOM.checkStrHasScriptTag(element, change.id, change.content);
    DOM.checkStrHasStyleTag(change.id, change.content);
    const newElementInStr = ChangeBuilder.build(change);
    const replaced = DOM.removeScriptAndStyleTag(newElementInStr);
    const container = document.createElement('div');
    container.innerHTML = replaced;
    const elements = container.querySelectorAll('*');
    elements.forEach((element) => {
      element.id = change.id;
      this.setUniqueAttributes(element, change);
    });
    const modifiedString = container.innerHTML;

    if (edit) {
      element.outerHTML = modifiedString;
    } else {
      element.insertAdjacentHTML(position, modifiedString);
    }
  }

  static replaceCSS(change) {
    DOM.removeStyleTags(change.id);
    const { css } = ChangeBuilder.build(change);
    DOM.createStyleTag(change.id, css);
  }

  static deleteChange(change) {
    DOM.removeElementById(change.id);
    DOM.removeScriptTags(change.id);
    DOM.removeStyleTags(change.id);
  }

  static toggleChanges({ change }) {
    const element = this.getElement(change);
    if (!element) return;

    const isShowing = DOM.getClassList(element).contains(OUTLINE_SHOW_CLASS);
    if (isShowing) {
      DOM.removeClass(element, OUTLINE_SHOW_CLASS);
    } else {
      DOM.addClass(element, OUTLINE_SHOW_CLASS);
    }
  }

  static createChange(selector, content) {
    return {
      id: generateRandomString(),
      selector,
      position: 'beforebegin',
      content,
      type: TYPES.INSERT_HTML,
    };
  }

  static getDefaultHTML() {
    return document.createElement('P').outerHTML;
  }

  static getElement(change) {
    return DOM.getElementById(change.id);
  }

  static getContent(change) {
    return { content: change.content };
  }
}
