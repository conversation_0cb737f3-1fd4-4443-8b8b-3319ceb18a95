import moment = require('moment');
import { getAccountSettings } from '../helpers/mongoHelper';
import ioRedisAdapter from '../helpers/ioRedisAdapter';

const isUniqueVisitorInMonth = (lastVisit: number): boolean => {
  if (!lastVisit) return true;
  return moment().startOf('month').unix() > lastVisit;
};

const handleRedisLimitCount = async (databaseId: number, clientId: string, lastVisit: number) => {
  if (!isUniqueVisitorInMonth(lastVisit)) return false;

  const result = true;
  await ioRedisAdapter.increaseUsedVisitor(databaseId);

  if (!clientId) return result;

  const settings = await getAccountSettings(databaseId);
  if (!settings) return result;

  if (!settings.features.includes('REDIS_LIMIT_COUNT')) return result;

  if (await ioRedisAdapter.isUniqueVisitor(databaseId, clientId)) {
    await ioRedisAdapter.setUniqueVisitor(databaseId, clientId);
    return true;
  }

  return false;
};

export { handleRedisLimitCount };
