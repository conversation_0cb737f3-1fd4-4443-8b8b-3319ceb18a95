import { getAccountSettings } from '../helpers/mongoHelper';
import ioRedisAdapter from '../helpers/ioRedisAdapter';
import { handleRedisLimitCount } from './redisLimitCount';

// Mock dependencies
jest.mock('moment', () => {
  const mockMoment = jest.fn(() => ({
    startOf: jest.fn(() => ({
      unix: jest.fn(() => **********), // 2023-01-01 00:00:00 UTC
    })),
  }));
  return mockMoment;
});

jest.mock('../helpers/mongoHelper', () => ({
  getAccountSettings: jest.fn(),
}));

jest.mock('../helpers/ioRedisAdapter', () => ({
  __esModule: true,
  default: {
    increaseUsedVisitor: jest.fn(),
    isUniqueVisitor: jest.fn(),
    setUniqueVisitor: jest.fn(),
  },
}));

describe('handleRedisLimitCount', () => {
  const databaseId = 12345;
  const clientId = 'test-client-id';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('isUniqueVisitorInMonth', () => {
    it('should return false when lastVisit is in the current month', async () => {
      // Setup lastVisit to be in the mocked current month (**********)
      const lastVisit = **********; // 2023-01-02 00:00:00 UTC
      const result = await handleRedisLimitCount(databaseId, clientId, lastVisit);
      expect(ioRedisAdapter.increaseUsedVisitor).not.toHaveBeenCalled();
      expect(result).toBe(false);
    });
  });

  describe('client ID handling', () => {
    it('should return true when clientId is not provided', async () => {
      const result = await handleRedisLimitCount(databaseId, '', 0);
      expect(ioRedisAdapter.increaseUsedVisitor).toHaveBeenCalledWith(databaseId);
      expect(result).toBe(true);
    });
  });

  describe('account settings handling', () => {
    it('should return true when account settings are not found', async () => {
      (getAccountSettings as jest.Mock).mockResolvedValue(null);

      const result = await handleRedisLimitCount(databaseId, clientId, 0);

      expect(ioRedisAdapter.increaseUsedVisitor).toHaveBeenCalledWith(databaseId);
      expect(getAccountSettings).toHaveBeenCalledWith(databaseId);
      expect(result).toBe(true);
    });

    it('should return true when REDIS_LIMIT_COUNT feature is not enabled', async () => {
      (getAccountSettings as jest.Mock).mockResolvedValue({
        features: [],
        billing: { package: 'PREMIUM' },
      });

      const result = await handleRedisLimitCount(databaseId, clientId, 0);

      expect(ioRedisAdapter.increaseUsedVisitor).toHaveBeenCalledWith(databaseId);
      expect(getAccountSettings).toHaveBeenCalledWith(databaseId);
      expect(result).toBe(true);
    });
  });

  describe('unique visitor handling', () => {
    beforeEach(() => {
      (getAccountSettings as jest.Mock).mockResolvedValue({
        features: ['REDIS_LIMIT_COUNT'],
      });
    });

    it('should return true when visitor is unique and set the visitor as unique', async () => {
      (ioRedisAdapter.isUniqueVisitor as jest.Mock).mockResolvedValue(true);

      const result = await handleRedisLimitCount(databaseId, clientId, 0);

      expect(ioRedisAdapter.increaseUsedVisitor).toHaveBeenCalledWith(databaseId);
      expect(ioRedisAdapter.isUniqueVisitor).toHaveBeenCalledWith(databaseId, clientId);
      expect(ioRedisAdapter.setUniqueVisitor).toHaveBeenCalledWith(databaseId, clientId);
      expect(result).toBe(true);
    });

    it('should return false when visitor is not unique', async () => {
      (ioRedisAdapter.isUniqueVisitor as jest.Mock).mockResolvedValue(false);

      const result = await handleRedisLimitCount(databaseId, clientId, 0);

      expect(ioRedisAdapter.increaseUsedVisitor).toHaveBeenCalledWith(databaseId);
      expect(ioRedisAdapter.isUniqueVisitor).toHaveBeenCalledWith(databaseId, clientId);
      expect(ioRedisAdapter.setUniqueVisitor).not.toHaveBeenCalled();
      expect(result).toBe(false);
    });
  });

  describe('integration tests', () => {
    it('should handle the complete flow for a unique visitor with REDIS_LIMIT_COUNT feature', async () => {
      (getAccountSettings as jest.Mock).mockResolvedValue({
        features: ['REDIS_LIMIT_COUNT'],
      });
      (ioRedisAdapter.isUniqueVisitor as jest.Mock).mockResolvedValue(true);

      const result = await handleRedisLimitCount(databaseId, clientId, 0);

      expect(ioRedisAdapter.increaseUsedVisitor).toHaveBeenCalledWith(databaseId);
      expect(getAccountSettings).toHaveBeenCalledWith(databaseId);
      expect(ioRedisAdapter.isUniqueVisitor).toHaveBeenCalledWith(databaseId, clientId);
      expect(ioRedisAdapter.setUniqueVisitor).toHaveBeenCalledWith(databaseId, clientId);
      expect(result).toBe(true);
    });

    it('should handle the complete flow for a non-unique visitor with REDIS_LIMIT_COUNT feature', async () => {
      (getAccountSettings as jest.Mock).mockResolvedValue({
        features: ['REDIS_LIMIT_COUNT'],
      });
      (ioRedisAdapter.isUniqueVisitor as jest.Mock).mockResolvedValue(false);

      const result = await handleRedisLimitCount(databaseId, clientId, 0);

      expect(ioRedisAdapter.increaseUsedVisitor).toHaveBeenCalledWith(databaseId);
      expect(getAccountSettings).toHaveBeenCalledWith(databaseId);
      expect(ioRedisAdapter.isUniqueVisitor).toHaveBeenCalledWith(databaseId, clientId);
      expect(ioRedisAdapter.setUniqueVisitor).not.toHaveBeenCalled();
      expect(result).toBe(false);
    });

    it('should handle the complete flow when REDIS_LIMIT_COUNT feature is not enabled', async () => {
      (getAccountSettings as jest.Mock).mockResolvedValue({
        features: [],
      });

      const result = await handleRedisLimitCount(databaseId, clientId, 0);

      expect(ioRedisAdapter.increaseUsedVisitor).toHaveBeenCalledWith(databaseId);
      expect(getAccountSettings).toHaveBeenCalledWith(databaseId);
      expect(ioRedisAdapter.isUniqueVisitor).not.toHaveBeenCalled();
      expect(ioRedisAdapter.setUniqueVisitor).not.toHaveBeenCalled();
      expect(result).toBe(true);
    });
  });
});
