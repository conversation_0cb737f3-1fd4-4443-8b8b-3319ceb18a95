import { ObjectID } from 'bson';

export default abstract class IntegrationSettings {
  protected baseSettings: any;
  protected global: any;
  protected specific: any;
  protected ID: ObjectID;
  protected type: string;

  constructor(settings: any) {
    this.baseSettings = settings;
    this.ID = settings.global._id;
    this.initializeGlobal(settings);
    this.initializeSpecific(settings);
    this.type = settings.global.type;
  }

  protected initializeGlobal({ global }) {
    this.global = global.data;
  }

  getID(): ObjectID {
    return this.ID;
  }

  getType(): string {
    return this.type;
  }

  getGlobal(key: string, defaultValue: any = null) {
    return this.global[key] !== undefined ? this.global[key] : defaultValue;
  }

  getSpecific(key: string, defaultValue: any = null) {
    return this.specific[key] !== undefined ? this.specific[key] : defaultValue;
  }

  setSpecific(key: string, value: any) {
    this.specific[key] = value;
  }

  get(key: string, defaultValue: any = null) {
    return this.global[key] !== undefined
      ? this.global[key]
      : this.specific[key] !== undefined
      ? this.specific[key]
      : defaultValue;
  }

  getBaseSettings() {
    return this.baseSettings;
  }

  // eslint-disable-next-line
  protected abstract initializeSpecific(settings);
}
