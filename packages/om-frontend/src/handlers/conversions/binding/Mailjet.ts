import { isValidDate } from '../../../helpers/dateFormats';

export default class Mailjet {
  private static readonly MAILJET_DATETIME = 'datetime';

  public static setBindings({
    body,
    campaignData,
    subscriber,
    bindings,
    mailjetFields,
    properties,
  }) {
    bindings.forEach((binding) => {
      const { fixValue, isFix, fieldId, externalId } = binding;

      const getExternalFieldType = mailjetFields[externalId];

      if (isFix) {
        if (externalId && !externalId.startsWith('__')) {
          properties[externalId] = fixValue;
        }
      } else if (externalId != null) {
        if (fieldId === 'url') {
          properties[externalId] = decodeURIComponent(body.visitor.url);
        } else if (fieldId === 'campaign_name') {
          properties[externalId] = campaignData.name;
        } else if (fieldId === 'variant_name') {
          properties[externalId] = campaignData.variantName;
        } else if (fieldId === 'email') {
          properties[externalId] = subscriber.email;
        } else if (fieldId === 'firstname') {
          properties[externalId] = subscriber.firstName;
        } else if (fieldId === 'lastname') {
          properties[externalId] = subscriber.lastName;
        } else if (['coupon_code', 'coupon_title'].includes(fieldId)) {
          const coupon = subscriber.customFields ? subscriber.customFields[fieldId] : '';
          if (coupon) {
            properties[externalId] = coupon;
          }
        } else if (!['email', 'firstname', 'lastname'].includes(fieldId)) {
          if (subscriber.customFields && subscriber.customFields[fieldId]) {
            const customFields = Array.isArray(subscriber.customFields[fieldId])
              ? subscriber.customFields[fieldId].join(', ')
              : this.getCustomFieldValue(subscriber, fieldId, getExternalFieldType);
            properties[externalId] = subscriber.customFields ? customFields : '';
          }
        }
      }
    });
  }

  private static getCustomFieldValue(subscriber, fieldId, fieldType) {
    let customFieldValue = subscriber.customFields[fieldId];

    if (fieldType === this.MAILJET_DATETIME) {
      const date = isValidDate(customFieldValue);
      if (date) {
        customFieldValue = date.format();
      }
    }

    return customFieldValue;
  }
}
