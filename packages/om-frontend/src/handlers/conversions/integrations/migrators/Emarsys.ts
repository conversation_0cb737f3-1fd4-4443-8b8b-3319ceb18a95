import IntegrationSettings from '../../settings/IntegrationSettings';
import EmarsysBinding from '../../binding/Emarsys';

export default (settings: IntegrationSettings) => {
  const fieldId = settings.getSpecific('fieldId');

  if (fieldId) {
    settings.setSpecific(
      'bindings',
      settings.getSpecific('bindings').concat([
        {
          isFix: true,
          fieldId: null,
          fixValue: settings.getSpecific('fieldValue') || 'OptiMonk',
          externalId: fieldId,
        },
      ]),
    );

    settings.setSpecific(
      'bindings',
      settings.getSpecific('bindings').concat([
        {
          isFix: false,
          fieldId: 'firstname',
          fixValue: null,
          externalId: EmarsysBinding.FIELD_ID.FIRSTNAME,
        },
        {
          isFix: false,
          fieldId: 'lastname',
          fixValue: null,
          externalId: EmarsysBinding.FIELD_ID.LASTNAME,
        },
        {
          isFix: false,
          fieldId: 'email',
          fixValue: null,
          externalId: EmarsysBinding.FIELD_ID.EMAIL,
        },
      ]),
    );
  }

  return settings;
};
