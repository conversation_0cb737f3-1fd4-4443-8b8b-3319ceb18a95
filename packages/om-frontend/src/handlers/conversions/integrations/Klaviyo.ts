import { ObjectId } from 'mongodb';
import { ISubscriber } from '../../AbstractConversionHandler';
import AbstractIntegration from './AbstractIntegration';
import HandlingResult from '../HandlingResult';
import IIntegrationAdapter from './IIntegrationAdapter';
import IntegrationResponse from '../IntegrationResponse';
import { isFeatureEnabled, FEATURES } from '../../../helpers/featureFlags';
import KlaviyoInstantConsent from './klaviyo/KlaviyoInstantConsent';
import KlaviyoBulkSubscribe from './klaviyo/KlaviyoBulkSubscribe';

export default class Klaviyo extends AbstractIntegration implements IIntegrationAdapter {
  getType() {
    return 'klaviyo';
  }

  private PHONE_NUMBER = 'phone_number';

  protected getPhoneNumberFromBindings(subscriber: ISubscriber) {
    const bindings = this.settings.getSpecific('bindings');
    const phoneBinding = bindings.find((binding) => binding.externalId === this.PHONE_NUMBER);
    return phoneBinding && subscriber.customFields
      ? subscriber.customFields[phoneBinding.fieldId]
      : '';
  }

  setMultiListId(email: string, phoneNumber: string) {
    const hasMultiList = this.settings.getSpecific('multiList');
    if (!hasMultiList) return;

    if (email && phoneNumber) {
      this.settings.setSpecific('listId', hasMultiList.emailAndPhone);
      return;
    }

    if (email) {
      this.settings.setSpecific('listId', hasMultiList.emailOnly);
      return;
    }

    this.settings.setSpecific('listId', hasMultiList.phoneOnly);
  }

  async handle(campaignId: ObjectId, subscriber: ISubscriber): Promise<HandlingResult> {
    const phoneNumber = this.getPhoneNumberFromBindings(subscriber);

    // handle multi-list
    this.setMultiListId(subscriber.email, phoneNumber);

    if (!subscriber.email && !phoneNumber) {
      return new HandlingResult(
        this.settings,
        new IntegrationResponse(200, 'Fake ok', ''),
        subscriber,
      );
    }

    if (
      this.settings.getGlobal('publicApiKey') &&
      (await isFeatureEnabled(this.databaseId, FEATURES.KLAVIYO_NEW_SYNCHRONOUS_API, true))
    ) {
      const klaviyoInstantConsent = new KlaviyoInstantConsent(
        this.settings,
        this.databaseId,
        this.body,
      );

      return klaviyoInstantConsent.handle(campaignId, subscriber);
    }

    const klaviyoBulkSubscribe = new KlaviyoBulkSubscribe(
      this.settings,
      this.databaseId,
      this.body,
    );

    return klaviyoBulkSubscribe.handle(campaignId, subscriber);
  }

  buildPayload() {}

  getAxiosConfig(): { [key: string]: any; baseURL: string } {
    return {
      baseURL: '',
    };
  }

  getSubscribeConfig(): {
    [key: string]: any;
    url: string;
    method: string;
  } {
    return {
      url: '',
      method: '',
    };
  }
}
