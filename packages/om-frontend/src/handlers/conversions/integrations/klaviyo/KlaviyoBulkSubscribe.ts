import { ObjectId } from 'mongodb';
import axios from 'axios';
import { ISubscriber } from '../../../AbstractConversionHandler';
import AbstractIntegration from '../AbstractIntegration';
import HandlingResult from '../../HandlingResult';
import IIntegrationAdapter from '../IIntegrationAdapter';
import IntegrationResponse from '../../IntegrationResponse';
import BindingHelper from './bindingHelper';
import { API_VERSION, PARTNER_KEY } from './config';
import { IPayload } from './types';

export default class KlaviyoBulkSubscribe
  extends AbstractIntegration
  implements IIntegrationAdapter
{
  getType() {
    return 'klaviyo';
  }

  private profile: { id?: string; email?: string; phoneNumber?: string } = {};
  private subscriptions = {
    email: false,
    sms: false,
  };

  async handle(campaignId: ObjectId, subscriber: ISubscriber): Promise<HandlingResult> {
    this.campaignData = await this.getCampaignData(campaignId, subscriber.variantId);

    if (await this.isOverLimit(subscriber)) {
      return new HandlingResult(
        this.settings,
        new IntegrationResponse(200, 'Fake ok', ''),
        subscriber,
      );
    }

    const response = await this.subscribe(subscriber);
    return new HandlingResult(this.settings, response, subscriber, this.buildError(response));
  }

  async upsertProfile(profile: any): Promise<string> {
    const axiosInstance = axios.create(this.getAxiosConfig());

    const {
      data: {
        data: { id },
      },
    } = await axiosInstance.request({
      method: 'post',
      url: '/profile-import',
      data: profile,
    });

    return id;
  }

  async subscribe(subscriber: ISubscriber): Promise<IntegrationResponse> {
    const integResponses: Array<{ response: IntegrationResponse; success: boolean }> = [];

    let config;

    try {
      config = this.buildUpsertProfilePayload(subscriber);
      this.profile.id = await this.upsertProfile(config);
      config = this.getSubscribeConfig();
      this.logInfo(`Klaviyo bulk subscription, databaseId: ${this.databaseId}`, config);

      this.axiosInstance = axios.create(this.getAxiosConfig());
      const response = await this.axiosInstance.request(config);
      integResponses.push({
        response: new IntegrationResponse(
          response.status,
          response.statusText,
          config.data,
          response,
        ),
        success: true,
      });
    } catch (e: any) {
      this.logWarn('SUBSCRIBE EXCEPTION', e.message);
      integResponses.push({
        response: new IntegrationResponse(
          e.response.status,
          e.response.statusText,
          config?.data || {},
          e.response,
        ),
        success: false,
      });
    }

    const failedIndex = integResponses.findIndex((r) => !r.success);
    const integrationResponse =
      failedIndex !== -1
        ? integResponses[failedIndex].response
        : integResponses.filter((r) => r.success)[0].response;
    return integrationResponse;
  }

  buildUpsertProfilePayload(subscriber: ISubscriber) {
    const profile: any = {};
    const bindings = this.settings.getSpecific('bindings');

    if (Array.isArray(bindings)) {
      BindingHelper.setBindings({
        body: this.body,
        campaignData: this.campaignData,
        subscriber,
        bindings,
        properties: profile,
      });
    }

    if (profile.email) {
      this.profile.email = profile.email;
      this.subscriptions.email = true;
    }

    const phoneNumber = profile.phone_number || profile.phone;

    if (phoneNumber) {
      this.profile.phoneNumber = phoneNumber;
      this.subscriptions.sms = true;
    }

    return {
      data: {
        type: 'profile',
        attributes: {
          ...profile,
        },
      },
    };
  }

  buildPayload() {
    const subscriptions: any = {};

    if (this.subscriptions.email) {
      subscriptions.email = {
        marketing: {
          consent: 'SUBSCRIBED',
        },
      };
    }

    if (this.subscriptions.sms) {
      subscriptions.sms = {
        marketing: {
          consent: 'SUBSCRIBED',
        },
      };
    }

    const payload: IPayload = {
      data: {
        type: 'profile-subscription-bulk-create-job',
        attributes: {
          profiles: {
            data: [
              {
                type: 'profile',
                id: this.profile.id,
                attributes: {
                  email: this.profile.email,
                  phone_number: this.profile.phoneNumber,
                  subscriptions,
                },
              },
            ],
          },
        },
      },
    };

    const listId = this.settings.getSpecific('listId');
    if (listId) {
      payload.data.relationships = {
        list: {
          data: {
            type: 'list',
            id: listId,
          },
        },
      };
    }

    return payload;
  }

  protected getAxiosConfig(): { [key: string]: any; baseURL: string } {
    return {
      baseURL: 'https://a.klaviyo.com/api',
      headers: {
        revision: API_VERSION,
        Authorization: `Klaviyo-API-Key ${this.settings.getGlobal('apiKey')}`,
        'Content-Type': 'application/json',
        'x-klaviyo-partner-key': PARTNER_KEY,
      },
    };
  }

  protected getSubscribeConfig(): {
    [key: string]: any;
    url: string;
    method: string;
  } {
    return {
      method: 'post',
      url: '/profile-subscription-bulk-create-jobs',
      data: this.buildPayload(),
    };
  }
}
