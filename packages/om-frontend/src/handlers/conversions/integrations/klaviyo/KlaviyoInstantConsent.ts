import { ObjectId } from 'mongodb';
import axios from 'axios';
import { ISubscriber } from '../../../AbstractConversionHandler';
import AbstractIntegration from '../AbstractIntegration';
import HandlingResult from '../../HandlingResult';
import IIntegrationAdapter from '../IIntegrationAdapter';
import IntegrationResponse from '../../IntegrationResponse';
import BindingHelper from './bindingHelper';
import { API_VERSION, PARTNER_KEY } from './config';
import { IPayload } from './types';

export default class KlaviyoInstantConsent
  extends AbstractIntegration
  implements IIntegrationAdapter
{
  getType() {
    return 'klaviyo';
  }

  async handle(campaignId: ObjectId, subscriber: ISubscriber): Promise<HandlingResult> {
    this.campaignData = await this.getCampaignData(campaignId, subscriber.variantId);

    if (await this.isOverLimit(subscriber)) {
      return new HandlingResult(
        this.settings,
        new IntegrationResponse(200, 'Fake ok', ''),
        subscriber,
      );
    }

    const response = await this.subscribe(subscriber);
    return new HandlingResult(this.settings, response, subscriber, this.buildError(response));
  }

  async subscribe(subscriber: ISubscriber): Promise<IntegrationResponse> {
    const integResponses: Array<{ response: IntegrationResponse; success: boolean }> = [];

    const config = this.getSubscribeConfig(subscriber);

    this.logInfo(`Klaviyo synchronous subscription, databaseId: ${this.databaseId}`, config);

    try {
      this.axiosInstance = axios.create(this.getAxiosConfig());
      const response = await this.axiosInstance.request(config);
      integResponses.push({
        response: new IntegrationResponse(
          response.status,
          response.statusText,
          config.data,
          response,
        ),
        success: true,
      });
    } catch (e: any) {
      this.logWarn('SUBSCRIBE EXCEPTION', e.message);
      integResponses.push({
        response: new IntegrationResponse(
          e.response.status,
          e.response.statusText,
          config.data,
          e.response,
        ),
        success: false,
      });
    }

    const failedIndex = integResponses.findIndex((r) => !r.success);
    const integrationResponse =
      failedIndex !== -1
        ? integResponses[failedIndex].response
        : integResponses.filter((r) => r.success)[0].response;
    return integrationResponse;
  }

  buildPayload(subscriber: ISubscriber) {
    const profile: any = {};
    const bindings = this.settings.getSpecific('bindings');

    if (Array.isArray(bindings)) {
      BindingHelper.setBindings({
        body: this.body,
        campaignData: this.campaignData,
        subscriber,
        bindings,
        properties: profile,
      });
    }

    const payload: IPayload = {
      data: {
        type: 'subscription',
        attributes: {
          profile: {
            data: {
              type: 'profile',
              attributes: profile,
            },
          },
        },
      },
    };

    const listId = this.settings.getSpecific('listId');
    if (listId) {
      payload.data.relationships = {
        list: {
          data: {
            type: 'list',
            id: listId,
          },
        },
      };
    }

    return payload;
  }

  protected getAxiosConfig(): { [key: string]: any; baseURL: string } {
    return {
      baseURL: 'https://a.klaviyo.com/',
      headers: {
        revision: API_VERSION,
        'Content-Type': 'application/json',
        'x-klaviyo-partner-key': PARTNER_KEY,
      },
    };
  }

  protected getSubscribeConfig(subscriber: any): {
    [key: string]: any;
    url: string;
    method: string;
  } {
    return {
      method: 'post',
      url: `/client/subscriptions?company_id=${this.settings.getGlobal('publicApiKey')}`,
      data: this.buildPayload(subscriber),
    };
  }
}
