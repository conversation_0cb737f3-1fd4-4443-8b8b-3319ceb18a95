import IIntegrationAdapter from './IIntegrationAdapter';
import IntegrationSettingsFactory from '../settings/IntegrationSettingsFactory';
import IntegrationSettings from '../settings/IntegrationSettings';
import {
  ActiveCampaign,
  Automizy,
  Klaviyo,
  Mailchimp,
  Webhook,
  Emarsys,
  Conversio,
  CampaignMonitor,
  ShopifyCustomer,
  SmsBump,
  MiniCRM,
  SalesAutopilot,
  ShopRenter,
  Omnisend,
  Marketo,
  Ontraport,
  WebGalamb4Plus,
  Copernica,
  Maileon,
  Slack,
  MailerLite,
  Moosend,
  Mailjet,
  MailWizz,
  Keap,
  Attentive,
  AttentiveV2,
  Dotmailer,
  Hubspot,
  AWeber,
  GetResponse,
  Mailigen,
  Salesforce,
  AcerCCDB,
  PostscriptLegacy,
  Postscript,
  SendGrid,
  Sendinblue,
  HubSpotV2,
  AcerCCDBV2,
  HighLevel,
  Recart,
  Selzy,
  TheMarketer,
  Unas,
  ListamesterV2,
  Zapier,
} from './index';

export default class IntegrationFactory {
  public static create(
    settings: { global: any; specific: any },
    databaseId: number,
    body: any,
  ): IIntegrationAdapter | null {
    const integrationSpecificSettings = IntegrationSettingsFactory.create(
      settings,
    ) as IntegrationSettings;
    switch (settings.global.type) {
      case 'activeCampaign':
        return new ActiveCampaign(integrationSpecificSettings, databaseId, body);
      case 'automizy':
        return new Automizy(integrationSpecificSettings, databaseId, body);
      case 'copernica':
        return new Copernica(integrationSpecificSettings, databaseId, body);
      case 'klaviyo':
        return new Klaviyo(integrationSpecificSettings, databaseId, body);
      case 'mailChimp':
        return new Mailchimp(integrationSpecificSettings, databaseId, body);
      case 'webhook':
        return new Webhook(integrationSpecificSettings, databaseId, body);
      case 'slack':
        return new Slack(integrationSpecificSettings, databaseId, body);
      case 'maileon':
        return new Maileon(integrationSpecificSettings, databaseId, body);
      case 'conversio':
      case 'cmcommerce':
        return new Conversio(integrationSpecificSettings, databaseId, body);
      case 'campaignMonitor':
        return new CampaignMonitor(integrationSpecificSettings, databaseId, body);
      case 'shopifyCustomer':
        return new ShopifyCustomer(integrationSpecificSettings, databaseId, body);
      case 'smsBump':
        return new SmsBump(integrationSpecificSettings, databaseId, body);
      case 'miniCrm':
        return new MiniCRM(integrationSpecificSettings, databaseId, body);
      case 'salesAutopilot':
        return new SalesAutopilot(integrationSpecificSettings, databaseId, body);
      case 'shopRenter':
        return new ShopRenter(integrationSpecificSettings, databaseId, body);
      case 'soundest':
      case 'omnisend':
        return new Omnisend(integrationSpecificSettings, databaseId, body);
      case 'marketo':
        return new Marketo(integrationSpecificSettings, databaseId, body);
      case 'ontraport':
        return new Ontraport(integrationSpecificSettings, databaseId, body);
      case 'webGalamb4Plus':
        return new WebGalamb4Plus(integrationSpecificSettings, databaseId, body);
      case 'mailerLite':
        return new MailerLite(integrationSpecificSettings, databaseId, body);
      case 'moosend':
        return new Moosend(integrationSpecificSettings, databaseId, body);
      case 'theMarketer':
        return new TheMarketer(integrationSpecificSettings, databaseId, body);
      case 'mailjet':
        return new Mailjet(integrationSpecificSettings, databaseId, body);
      case 'mailWizz':
        return new MailWizz(integrationSpecificSettings, databaseId, body);
      case 'keap':
        return new Keap(integrationSpecificSettings, databaseId, body);
      case 'attentive':
        return new Attentive(integrationSpecificSettings, databaseId, body);
      case 'attentiveV2':
        return new AttentiveV2(integrationSpecificSettings, databaseId, body);
      case 'dotmailer':
        return new Dotmailer(integrationSpecificSettings, databaseId, body);
      case 'hubSpot':
        return new Hubspot(integrationSpecificSettings, databaseId, body);
      case 'aWeber':
        return new AWeber(integrationSpecificSettings, databaseId, body);
      case 'getResponse':
        return new GetResponse(integrationSpecificSettings, databaseId, body);
      case 'mailigen':
        return new Mailigen(integrationSpecificSettings, databaseId, body);
      case 'salesforce':
        return new Salesforce(integrationSpecificSettings, databaseId, body);
      case 'acerCCDB':
        return new AcerCCDB(integrationSpecificSettings, databaseId, body);
      case 'emarsys':
        return new Emarsys(integrationSpecificSettings, databaseId, body);
      case 'postscriptLegacy':
        return new PostscriptLegacy(integrationSpecificSettings, databaseId, body);
      case 'postscript':
        return new Postscript(integrationSpecificSettings, databaseId, body);
      case 'sendGrid':
        return new SendGrid(integrationSpecificSettings, databaseId, body);
      case 'sendinblue':
        return new Sendinblue(integrationSpecificSettings, databaseId, body);
      case 'hubSpotV2':
        return new HubSpotV2(integrationSpecificSettings, databaseId, body);
      case 'acerCCDBV2':
        return new AcerCCDBV2(integrationSpecificSettings, databaseId, body);
      case 'highLevel':
        return new HighLevel(integrationSpecificSettings, databaseId, body);
      case 'recart':
        return new Recart(integrationSpecificSettings, databaseId, body);
      case 'selzy':
        return new Selzy(integrationSpecificSettings, databaseId, body);
      case 'unas':
        return new Unas(integrationSpecificSettings, databaseId, body);
      case 'listamesterV2':
        return new ListamesterV2(integrationSpecificSettings, databaseId, body);
      case 'zapier':
        return new Zapier(integrationSpecificSettings, databaseId, body);
    }
    return null;
  }
}
