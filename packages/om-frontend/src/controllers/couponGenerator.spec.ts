/* eslint-disable import/first */
const mockMongo = { getShopifyAccessToken: jest.fn(), getCouponConfig: jest.fn() };
const mockShopifyAdapter = { createPriceRule: jest.fn(), createCouponCode: jest.fn() };
jest.mock('../helpers/mongoHelper', () => mockMongo);
jest.mock('../helpers/shopifyAdapter', () => {
  return {
    ShopifyAdapter: jest.fn().mockImplementation(() => {
      return mockShopifyAdapter;
    }),
  };
});
jest.mock('../helpers/jfHelper', () => {
  return {
    reportCouponGeneration: jest.fn().mockResolvedValue(undefined),
  };
});

import { Request, Response } from 'express';
import { couponGeneratorController } from './couponGenerator';

describe('couponGenerator', () => {
  let req;
  let res;
  let end;
  beforeEach(() => {
    req = {
      header: {
        'user-agent': 'iOS',
      },
      body: {
        shopName: 'industructibleshoes',
        databaseId: 1,
        variantId: 'somevariantid',
        elementId: 'someelementid',
        deviceType: 'desktop',
      },
    };
    res = {
      // eslint-disable-next-line no-unused-vars
      status: jest.fn((code: number): Response => end as Response),
      send: jest.fn(),
    } as Partial<Response>;
    end = {
      end: jest.fn(),
    } as Partial<Response>;
  });
  test('should return error when required parameters are missing', async () => {
    req = {
      body: {},
    };
    await couponGeneratorController(req as Request, res as Response);

    expect(res.status).toHaveBeenCalledWith(400);
    expect(end.end).toHaveBeenCalledWith(
      'Missing required parameter(s): shopName, databaseId, variantId, elementId, deviceType',
    );
  });

  test('should return error if databaseId is not a number', async () => {
    req = {
      body: {
        shopName: 'industructibleshoes',
        databaseId: 'someId',
        variantId: 'somevariantid',
        elementId: 'someelementid',
        deviceType: 'desktop',
      },
    };

    await couponGeneratorController(req as Request, res as Response);

    expect(res.status).toHaveBeenCalledWith(400);
    expect(end.end).toHaveBeenCalledWith('databaseId should be a number!');
  });

  test('should return error if token is not available', async () => {
    mockMongo.getShopifyAccessToken.mockImplementation(() => null);

    await couponGeneratorController(req as Request, res as Response);

    expect(res.status).toHaveBeenCalledWith(500);
    expect(end.end).toHaveBeenCalledWith('Could not create coupon code for industructibleshoes');
  });

  test('should return error if couponconfig is not available', async () => {
    mockMongo.getShopifyAccessToken.mockImplementation(() => 'authtoken');
    mockMongo.getCouponConfig.mockImplementation(() => null);

    await couponGeneratorController(req as Request, res as Response);

    expect(res.status).toHaveBeenCalledWith(500);
    expect(end.end).toHaveBeenCalledWith('Could not create coupon code for industructibleshoes');
  });

  test('should return error if price rule cannot be created', async () => {
    mockMongo.getShopifyAccessToken.mockImplementation(() => 'authtoken');
    mockMongo.getCouponConfig.mockImplementation(() => {
      return {
        automatic: {
          expiresIn: 72,
          prefix: 'COUPON',
          value: 200,
          type: 'fix',
          minCartValue: 500,
        },
      };
    });

    mockShopifyAdapter.createPriceRule.mockRejectedValue(
      new Error('error happened while creating price rule'),
    );

    await couponGeneratorController(req as Request, res as Response);

    expect(res.status).toHaveBeenCalledWith(500);
    expect(end.end).toHaveBeenCalledWith('Could not create coupon code for industructibleshoes');
  });

  test('should return error if coupon code cannot be created', async () => {
    mockMongo.getShopifyAccessToken.mockImplementation(() => 'authtoken');
    mockMongo.getCouponConfig.mockImplementation(() => {
      return {
        automatic: {
          expiresIn: 72,
          prefix: 'COUPON',
          value: 200,
          type: 'fix',
          minCartValue: 500,
        },
      };
    });

    mockShopifyAdapter.createPriceRule.mockResolvedValue({
      priceRuleID: 123,
      generatedAt: '2012-12-12',
      validUntil: '2022-12-12',
    });

    mockShopifyAdapter.createCouponCode.mockRejectedValue(
      new Error('error happened while creating coupon code'),
    );

    await couponGeneratorController(req as Request, res as Response);

    expect(res.status).toHaveBeenCalledWith(500);
    expect(end.end).toHaveBeenCalledWith('Could not create coupon code for industructibleshoes');
  });

  test('should return generated coupon data', async () => {
    mockMongo.getShopifyAccessToken.mockImplementation(() => 'authtoken');
    mockMongo.getCouponConfig.mockImplementation(() => {
      return {
        automatic: {
          expiresIn: 72,
          prefix: 'COUPON',
          value: 200,
          type: 'fix',
          minCartValue: 500,
        },
      };
    });

    mockShopifyAdapter.createPriceRule.mockResolvedValue({
      priceRuleID: 123,
      generatedAt: '2012-12-12',
      validUntil: '2022-12-12',
    });

    mockShopifyAdapter.createCouponCode.mockResolvedValue(undefined);

    await couponGeneratorController(req as Request, res as Response);

    expect(res.send).toHaveBeenCalledWith({
      code: expect.any(String),
      generatedAt: '2012-12-12',
      platform: 'shopify',
      validUntil: '2022-12-12',
    });
  });
});
