import { reportUserAlert } from '../../Utils/report/ReportUserAlert';

const validator = {
  isReported: false,
  isExpired(dateFrom) {
    if (!dateFrom) return false;

    return new Date(dateFrom) < new Date();
  },
  validate(campaign) {
    const countdowns = campaign.countdowns ?? [];
    const expires = countdowns.map(({ initData: { date } }) => this.isExpired(date));
    const isExpired = expires.some(Boolean);

    if (isExpired) {
      if (!this.isReported) {
        reportUserAlert({
          type: 'CountdownInThePast',
          links: { campaign },
          context: {
            countdowns: countdowns.map((countdown) => countdown.initData),
          },
        });
        this.isReported = true;
      }
      return false;
    }

    return true;
  },
};
export const CountdownValidator = {
  type: 'countdown',
  validate(campaign) {
    return validator.validate(campaign);
  },
};
