import { Replacer } from '../Replacer';

const replace = (element) => {
  const contents = element.querySelectorAll('.om-dtr-content');
  if (!contents.length) return;
  for (let index = 0; index < contents.length; index += 1) {
    const element = contents[index];
    const smartTags = [...element.querySelectorAll('.smart-tag')];
    smartTags.map((smartTag) => Replacer.replaceSmartTag(smartTag));
    const html = element.innerHTML;
    const replaced = Replacer.replaceText(html);
    if (html !== replaced) {
      element.innerHTML = replaced;
    }
  }
};
export const Text = {
  replace,
};
