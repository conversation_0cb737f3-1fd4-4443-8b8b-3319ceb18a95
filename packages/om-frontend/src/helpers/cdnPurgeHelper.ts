import { getHashedFileNames } from './fileVersioningHelper';
import <PERSON>CD<PERSON> from './bunnyCDNAdapter';

const getStaticFiles = () => {
  const hashedFiles = getHashedFileNames();
  hashedFiles.embeddedIntlTelInput = 'embeddedIntlTelInput.min.js';
  hashedFiles.embeddedFlatpickr = 'embeddedFlatpickr.min.js';

  return hashedFiles;
};

const getMainScript = () => {
  return BunnyCDN.getMainScript();
};

export { getStaticFiles, getMainScript };
