export const getUsedVisitorKey = () => `frontend:usedVisitor`;
export const getPageViewsKey = () => `frontend:pageViews`;
export const getUniqueDigicelKey = () => `uniqueVisitor:digicel`;
export const getUniqueTurbodietaKey = () => `uniqueVisitor:turbodieta`;
export const getUniquePremiumShop24Key = () => `uniqueVisitor:premiumShop24`;
export const getTroisSoixanteSubKey = () => `uniqueVisitor:triosSoixanteSub137506`;
export const getUniqueVisitorKey = (databaseId) => `uniqueVisitor:${databaseId}`;
export const getRoadRecordKey = () => `uniqueVisitor:roadRecord`;
export const getStatisticsKey = () => `statistics`;
export const getShopAttributesKey = (shopType, shopName, key, value) =>
  `shopAttributes:${shopType}:${shopName}:${key}:${value}`;
export const getPreviewCacheKey = (accountId, campaignId, variantId) =>
  `preview:${accountId}:${campaignId}:${variantId}`;
export const getPreviewV3CacheKey = (accountId, variantId) => `preview:${accountId}:${variantId}`;
export const getAllCampaignsCacheKey = (accountId, device, domainHash) =>
  `frontend:${accountId}:campaign:active:${device}:${domainHash}`;
export const getAllCampaignsTypesCacheKey = (accountId) => `frontend:${accountId}:variant:types`;
export const getVariantCacheKey = (accountId, campaignId, variantId) =>
  `frontend:hot:${accountId}:campaign:${campaignId}:${variantId}`;
export const getActiveEmbeddedV3CampaignsKey = (accountId) =>
  `frontend:${accountId}:campaign:embedded:active`;
export const getActiveDynamicContentCampaignsKey = (accountId) =>
  `frontend:${accountId}:campaign:dynamic-content:active`;
export const getEmbeddedVariantCacheKey = (accountId, variantId) =>
  `frontend:hot:${accountId}:campaign:embedded:${variantId}`;
export const getDomainsCacheKey = (accountId) => `frontend:${accountId}:domains`;
export const getProcessedActiveVariantCacheKey = (accountId, campaignIds) =>
  `frontend:${accountId}:campaign:${campaignIds.join('-')}:processedActiveVariants`;
export const getConversionValidationKey = (identifier) =>
  `frontend:conversionValidation:${identifier}`;
export const getClientSideScraperLockKey = (urlHash) =>
  `frontend:clientSideScraper:lock:${urlHash}`;
