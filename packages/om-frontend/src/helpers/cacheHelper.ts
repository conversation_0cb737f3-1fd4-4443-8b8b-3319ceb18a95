import RedisLock = require('ioredis-lock');
import { performance } from 'perf_hooks';
import { Device } from '../types/enums';
import { ICampaign } from '../types/interfaces';
import { FEATURES, isFeatureEnabled } from './featureFlags';
import ioRedisAdapter, { getAndDellPageViewsAndUserVisitors } from './ioRedisAdapter';
import { pino as logger } from './logger';
import * as mongoHelper from './mongoHelper';
import { getAllCampaignsCacheKey } from './redisKeys';
import { getAccountSettings } from './mongoHelper';
import { handleRedisLimitCount } from '../services/redisLimitCount';

const crypto = require('crypto');

const frontendFeatureFlags = [
  FEATURES.STOP_ON_INTEGRATION_ERROR,
  FEATURES.SHADOW_CAMPAIGNS,
  FEATURES.FRONTEND_NO_MODULE,
  FEATURES.BUNNY_FONTS,
  FEATURES.TRACK_JS,
  FEATURES.SMART_FONT_LOADER,
  FEATURES.SMART_PAGE_TAG,
  FEATURES.SMART_PRODUCT_TAG,
  FEATURES.SPPO_RESEARCH_CDN,
  FEATURES.DISCOUNT_CODE_ASYNC,
  FEATURES.CURRENT_URL_2024,
  FEATURES.VISITOR_CART_V3,
  FEATURES.BLOCK_LINUX_VISITORS,
  FEATURES.FREQUENCY_RULE_V2,
];

export const storeInRedisRandom = async (key: string, data: any) => {
  const ttl = 3600 + Math.floor(Math.random() * 60 * 30);
  await ioRedisAdapter.setex(key, JSON.stringify(data), ttl);
};

export const writePageViewsAndUsedVisitorToDb = async () => {
  const { usedVisitor, pageViews } = await getAndDellPageViewsAndUserVisitors();
  return mongoHelper.writePageViewsAndUsedVisitorToDb(pageViews, usedVisitor);
};

export const handleUsedVisitor = async (accountId: number, clientId: string, lastVisit: number) => {
  let isNewVisitor = false;

  if (await handleRedisLimitCount(accountId, clientId, lastVisit)) {
    isNewVisitor = true;
  }

  return isNewVisitor;
};

export const QUERY_TIME_LIMIT = 40000;
export const getAllCampaignsForAccount = async (
  accountId: number,
  device: Device,
  domainIds: any[],
) => {
  const domainIdsConcat = domainIds.reduce((acc, x) => acc + x._id, '');
  const domainHash = crypto.createHash('sha1').update(domainIdsConcat).digest().toString('hex');
  const key = getAllCampaignsCacheKey(accountId, device, domainHash);
  const redisCampaigns = await ioRedisAdapter.get(key);
  let allCampaign: ICampaign[] = [];
  if (!redisCampaigns) {
    const lock = RedisLock.createLock(ioRedisAdapter.redis, { timeout: QUERY_TIME_LIMIT });
    try {
      await lock.acquire(`${key}:lock`);
      allCampaign = await mongoHelper.getAllCampaignsByAccountId(accountId, device, domainIds);
      const startTime = performance.now();
      logger.info({
        domainIds,
        device,
        accountId,
        took: performance.now() - startTime,
        message: `getAllCampaignsByAccountId ${accountId}`,
      });
      await storeInRedisRandom(key, allCampaign);
      await lock.release();
    } catch (e: any) {
      if (e.name !== 'LockAcquisitionError') {
        logger.error(e);
      }
    }
  } else {
    allCampaign = JSON.parse(redisCampaigns);
  }
  return allCampaign;
};

export const removeAccountSettingsCache = async (accountId: number) => {
  const cacheKey = `frontend:${accountId}:settings`;
  return ioRedisAdapter.del(cacheKey);
};

export const loadFeatureFlags = async (accountId: number) => {
  const accountSettings = await getAccountSettings(accountId);

  const flags = accountSettings?.features
    .filter((flag) => frontendFeatureFlags.includes(flag))
    .sort()
    .reduce((acc, flag) => {
      return {
        ...acc,
        [flag]: true,
      };
    }, {});

  return flags || {};
};

export const loadPreloadFeatureFlags = async (accountId: number) => {
  return loadFeatures(accountId, frontendFeatureFlags);
};

const loadFeatures = async (accountId: number, featuresToQuery: string[]) => {
  const featuresArray = await Promise.all(
    featuresToQuery.map((feature) => {
      return isFeatureEnabled(accountId, feature, true);
    }),
  );

  return featuresToQuery.reduce((prev, featureQuery, index) => {
    if (featuresArray[index]) {
      return {
        ...prev,
        [featureQuery]: true,
      };
    }

    return prev;
  }, {});
};
