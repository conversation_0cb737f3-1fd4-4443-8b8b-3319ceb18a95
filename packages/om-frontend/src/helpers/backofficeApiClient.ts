import axios, { AxiosRequestConfig } from 'axios';
import * as https from 'https';
import * as querystring from 'querystring';

const OM_BACKOFFICE_URL = process.env.BACKOFFICE_DOMAIN;
const USERNAME = process.env.BACKOFFICE_USERNAME;
const PASSWORD = process.env.BACKOFFICE_PASSWORD;

function buildUrl() {
  return `${OM_BACKOFFICE_URL}/api`;
}

const agent = new https.Agent({
  rejectUnauthorized: false,
});

const options = {
  httpsAgent: agent,
  auth: {},
} as AxiosRequestConfig;

if (USERNAME && PASSWORD) {
  options.auth = {
    username: USERNAME,
    password: PASSWORD,
  };
}

class BackofficeApiClient {
  static async post(resource, data) {
    return axios.post(buildUrl() + resource, querystring.stringify(data), options);
  }

  static async put(resource, data) {
    return axios.put(buildUrl() + resource, querystring.stringify(data), options);
  }

  static async get(resource) {
    return axios.get(buildUrl() + resource, options);
  }

  static async delete(resource) {
    return axios.delete(buildUrl() + resource, options);
  }
}

export default BackofficeApiClient;
