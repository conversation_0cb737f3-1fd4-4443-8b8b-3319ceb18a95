import { handleUsedVisitor } from './cacheHelper';
import { handleRedisLimitCount } from '../services/redisLimitCount';

// Mock dependencies
jest.mock('./ioRedisAdapter');

// Mock handleRedisLimitCount
jest.mock('../services/redisLimitCount');
// Import the mocked module

// Mock moment to control time-based tests
jest.mock('moment', () => {
  const mockMomentObj = {
    startOf: jest.fn().mockReturnThis(),
    unix: jest.fn().mockReturnValue(1640995200), // 2022-01-01 00:00:00 UTC,
    subtract: jest.fn().mockReturnThis(),
    format: jest.fn().mockReturnValue('2022-01-01'),
  };
  const mockMoment: any = jest.fn(() => mockMomentObj);
  mockMoment.utc = jest.fn().mockReturnValue(mockMoment);
  return mockMoment;
});

describe('cacheHelper', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Set default mock implementation
    (handleRedisLimitCount as jest.Mock).mockResolvedValue(true);
  });

  describe('handleUsedVisitor', () => {
    const clientId = 'test-client-id';

    // Account IDs for special cases
    const digicelAccountId = 10610;
    const turbodietaAccountId = 112649;
    const troisSoixanteSubAccountId = 137506;
    const premiumShop24AccountId = 190738;
    const roadRecordAccountId = 199139;
    const regularAccountId = 12345;

    // Test cases for regular account
    test('should return true for a new visitor with regular account', async () => {
      // Last visit is null (new visitor)
      const lastVisit = 0; // Use 0 instead of null to satisfy TypeScript

      const result = await handleUsedVisitor(regularAccountId, clientId, lastVisit);

      expect(result).toBe(true);
      expect(handleRedisLimitCount).toHaveBeenCalledWith(regularAccountId, clientId, lastVisit);
    });

    test('should return true for a visitor from previous month with regular account', async () => {
      // Last visit is from previous month (********** = 2021-12-31 00:00:00 UTC)
      const lastVisit = **********;

      const result = await handleUsedVisitor(regularAccountId, clientId, lastVisit);

      expect(result).toBe(true);
      expect(handleRedisLimitCount).toHaveBeenCalledWith(regularAccountId, clientId, lastVisit);
    });

    test('should return false for a visitor from current month with regular account', async () => {
      // Last visit is from current month (********** = 2022-01-02 00:00:00 UTC)
      const lastVisit = **********;

      // Mock handleRedisLimitCount to return undefined for this test
      (handleRedisLimitCount as jest.Mock).mockResolvedValue(undefined);

      const result = await handleUsedVisitor(regularAccountId, clientId, lastVisit);

      expect(result).toBe(false);
      expect(handleRedisLimitCount).toHaveBeenCalledWith(regularAccountId, clientId, lastVisit);
    });

    // Test cases for Digicel account
    test('should return true for a new unique visitor with Digicel account', async () => {
      // Last visit is 0 (new visitor)
      const lastVisit = 0;

      const result = await handleUsedVisitor(digicelAccountId, clientId, lastVisit);

      expect(result).toBe(true);
      expect(handleRedisLimitCount).toHaveBeenCalledWith(digicelAccountId, clientId, lastVisit);
    });

    test('should return false for a non-unique visitor with Digicel account', async () => {
      // Last visit is null (new visitor)
      const lastVisit = 0; // Use 0 instead of null to satisfy TypeScript

      // Mock handleRedisLimitCount to return undefined for this test
      (handleRedisLimitCount as jest.Mock).mockResolvedValue(undefined);

      const result = await handleUsedVisitor(digicelAccountId, clientId, lastVisit);

      expect(result).toBe(false);
      expect(handleRedisLimitCount).toHaveBeenCalledWith(digicelAccountId, clientId, lastVisit);
    });

    // Test cases for Turbodieta account
    test('should return true for a new unique visitor with Turbodieta account', async () => {
      // Last visit is null (new visitor)
      const lastVisit = 0; // Use 0 instead of null to satisfy TypeScript

      const result = await handleUsedVisitor(turbodietaAccountId, clientId, lastVisit);

      expect(result).toBe(true);
      expect(handleRedisLimitCount).toHaveBeenCalledWith(turbodietaAccountId, clientId, lastVisit);
    });

    // Test cases for TroisSoixanteSub account
    test('should return true for a new unique visitor with TroisSoixanteSub account', async () => {
      // Last visit is null (new visitor)
      const lastVisit = 0; // Use 0 instead of null to satisfy TypeScript

      const result = await handleUsedVisitor(troisSoixanteSubAccountId, clientId, lastVisit);

      expect(result).toBe(true);
      expect(handleRedisLimitCount).toHaveBeenCalledWith(
        troisSoixanteSubAccountId,
        clientId,
        lastVisit,
      );
    });

    // Test cases for PremiumShop24 account
    test('should return true for a new unique visitor with PremiumShop24 account', async () => {
      // Last visit is null (new visitor)
      const lastVisit = 0; // Use 0 instead of null to satisfy TypeScript

      const result = await handleUsedVisitor(premiumShop24AccountId, clientId, lastVisit);

      expect(result).toBe(true);
      expect(handleRedisLimitCount).toHaveBeenCalledWith(
        premiumShop24AccountId,
        clientId,
        lastVisit,
      );
    });

    // Test cases for RoadRecord account
    test('should return true for a new unique visitor with RoadRecord account', async () => {
      // Last visit is null (new visitor)
      const lastVisit = 0; // Use 0 instead of null to satisfy TypeScript

      const result = await handleUsedVisitor(roadRecordAccountId, clientId, lastVisit);

      expect(result).toBe(true);
      expect(handleRedisLimitCount).toHaveBeenCalledWith(roadRecordAccountId, clientId, lastVisit);
    });

    // Edge cases
    test('should handle missing clientId', async () => {
      const lastVisit = 0; // Use 0 instead of null to satisfy TypeScript
      const emptyClientId = '';

      const result = await handleUsedVisitor(regularAccountId, emptyClientId, lastVisit);

      expect(result).toBe(true);
      expect(handleRedisLimitCount).toHaveBeenCalledWith(
        regularAccountId,
        emptyClientId,
        lastVisit,
      );
    });

    test('should handle undefined lastVisit', async () => {
      const lastVisit = undefined as unknown as number; // Cast to number to satisfy TypeScript

      const result = await handleUsedVisitor(regularAccountId, clientId, lastVisit);

      expect(result).toBe(true);
      expect(handleRedisLimitCount).toHaveBeenCalledWith(regularAccountId, clientId, lastVisit);
    });

    test('should handle non-numeric lastVisit', async () => {
      const lastVisit = 'not-a-number' as unknown as number; // Cast to number to satisfy TypeScript

      // Mock handleRedisLimitCount to return undefined for this test
      (handleRedisLimitCount as jest.Mock).mockResolvedValue(undefined);

      const result = await handleUsedVisitor(regularAccountId, clientId, lastVisit);

      expect(result).toBe(false);
      expect(handleRedisLimitCount).toHaveBeenCalledWith(regularAccountId, clientId, lastVisit);
    });
  });
});
