/* eslint-disable import/first */
const mockUpsertUserAlert = jest.fn();
const mockFindOne = jest.fn();
const mockSecondaryDbConnection = {
  collection: () => {
    return { findOne: mockFindOne };
  },
};

import { ObjectID } from 'mongodb';
import { handleCountdownAlert } from './userAlertHelper';

jest.mock('./mongoHelper', () => {
  return { upsertUserAlert: mockUpsertUserAlert };
});

jest.mock('./SecondaryMongoHelper', () => {
  return { secondaryDbConnection: mockSecondaryDbConnection };
});

describe('userAlertHelper tests', () => {
  beforeEach(() => {
    jest.useFakeTimers().setSystemTime(new Date('2024-12-03T08:59:02.000Z'));
  });
  afterEach(() => {
    jest.useRealTimers();
  });
  test('handleCountdownAlert', async () => {
    mockFindOne.mockResolvedValue({
      status: 'active',
      schedule: {
        tz: 'Europe/Budapest',
        from: '2024-12-03T08:59:00.000Z',
        to: '2024-12-03T09:00:00.000Z',
        repeatsOn: [
          {
            daysOfWeek: [0, 1, 2, 3, 4, 5, 6],
            tz: 'Europe/Budapest',
            fromTime: null,
            toTime: null,
          },
        ],
      },
      variants: [
        {
          _id: new ObjectID('614b22a057e5cf133d4f84e0'),
          template: {
            elements: [
              {
                type: 'OmCountdown',
                data: {
                  type: 'countdown',
                  countdown: {
                    type: 'date',
                    endDate: {
                      date: 'Tue, 03 Dec 2024 04:08:53 GMT',
                      type: 'time',
                    },
                  },
                },
              },
            ],
          },
        },
      ],
    });
    const userAlertPayload = {
      databaseId: 44,
      type: 'CountdownInThePast',
      links: [
        {
          id: '654b22a057e5cf133d4f84e0',
          type: 'Campaign',
        },
        {
          id: '614b22a057e5cf133d4f84e0',
          type: 'Variant',
        },
      ],
      context: {},
    };
    await handleCountdownAlert(userAlertPayload);
    expect(mockUpsertUserAlert).toHaveBeenCalledWith(userAlertPayload);
  });
  test('handleCountdownAlert VARIANT TEMPLATE MIGRATED', async () => {
    mockFindOne
      .mockResolvedValueOnce({
        features: ['VARIANT_TEMPLATE_MIGRATED'],
      })
      .mockResolvedValueOnce({
        id: 1,
        status: 'active',
        schedule: {
          tz: 'Europe/Budapest',
          from: '2024-12-03T08:59:00.000Z',
          to: '2024-12-03T09:00:00.000Z',
          repeatsOn: [
            {
              daysOfWeek: [0, 1, 2, 3, 4, 5, 6],
              tz: 'Europe/Budapest',
              fromTime: null,
              toTime: null,
            },
          ],
        },
        variants: [
          {
            _id: new ObjectID('614b22a057e5cf133d4f84e0'),
            template: null,
          },
        ],
      })
      .mockResolvedValueOnce({
        template: {
          elements: [
            {
              type: 'OmCountdown',
              data: {
                type: 'countdown',
                countdown: {
                  type: 'date',
                  endDate: {
                    date: 'Tue, 03 Dec 2024 04:08:53 GMT',
                    type: 'time',
                  },
                },
              },
            },
          ],
        },
      });
    const userAlertPayload = {
      databaseId: 44,
      type: 'CountdownInThePast',
      links: [
        {
          id: '654b22a057e5cf133d4f84e0',
          type: 'Campaign',
        },
        {
          id: '614b22a057e5cf133d4f84e0',
          type: 'Variant',
        },
      ],
      context: {},
    };
    await handleCountdownAlert(userAlertPayload);
    expect(mockUpsertUserAlert).toHaveBeenCalledWith(userAlertPayload);
  });
});
