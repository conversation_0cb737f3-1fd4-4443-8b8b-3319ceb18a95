import { Redis as RedisClient, RedisOptions } from 'ioredis';

import * as config from '../config';
import { pino as logger } from './logger';
import {
  getConversionValidationKey,
  getPageViewsKey,
  getPreviewCacheKey,
  getPreviewV3CacheKey,
  getRoadRecordKey,
  getShopAttributesKey,
  getStatisticsKey,
  getTroisSoixanteSubKey,
  getUniqueDigicelKey,
  getUniquePremiumShop24Key,
  getUniqueTurbodietaKey,
  getUsedVisitorKey,
  getUniqueVisitorKey,
} from './redisKeys';
// tslint:disable-next-line: no-var-requires
const Redis = require('ioredis');

const _clients = {};
export const createClient = (
  type?: 'subscriber' | 'client' | 'bclient',
  options?: RedisOptions,
): RedisClient => {
  if (!type || type === 'bclient') {
    return new Redis({ ...options, host: config.redisHost, port: 6379 });
  }

  _clients[type] = _clients[type] || new Redis({ ...options, host: config.redisHost, port: 6379 });
  _clients[type].setMaxListeners(40);

  return _clients[type];
};

const redis = new Redis(6379, config.redisHost);
if (!redis) console.log('WARNING: no redis connection');

export const ONE_DAY = 24 * 60 * 60;

const inited = () => redis !== null;

const del = (key: string) => {
  return redis.del(key);
};

const mdel = async (keys: string[]) => {
  return redis.del(...keys);
};

const get = async (key: string) => {
  const start = Date.now();
  const result = await redis.get(key);
  const took = Date.now() - start;

  if (took >= 200) logger.info({ message: `slowRedisOperation GET ${key}`, key, took });

  return result;
};

const setexnx = (key: string, value: any, ttl: number) => {
  return redis.set(key, value, 'EX', ttl, 'NX');
};

const set = (key: string, value: any) => {
  return redis.set(key, value);
};

const setex = (key: string, value: any, seconds: number = config.redisExpire) => {
  return redis.setex(key, seconds, value);
};

const setnx = (key: string, value: any) => {
  return redis.setnx(key, value);
};

const multiSetnxTtl = async (rows) => {
  return rows
    .reduce((acc, row) => acc.setnx(row.key, row.value).expire(row.key, row.ttl), redis.multi())
    .exec();
};

const expire = async (key: string, seconds: number = config.redisExpire) => {
  return redis.expire(key, seconds);
};

const ttl = async (key) => {
  return redis.ttl(key);
};

const hset = (hash, key, value) => {
  return redis.hset(hash, key, value);
};

const hget = async (hash, key) => {
  return redis.hget(hash, key);
};

const mset = (keyValuePairs: any[]) => {
  const [key, value] = keyValuePairs.splice(0, 2);
  return redis.mset(key, value, ...keyValuePairs);
};

const mget = async (keys: string[]) => {
  return redis.mget(...keys);
};

const mgetWithTtl = async (keys: string[]) => {
  const raw = await keys.reduce((acc, key) => acc.get(key).ttl(key), redis.multi()).exec();
  return raw.reduce((results, value, index) => {
    if (index % 2 === 0) {
      results.push({
        key: keys[Math.floor(index / 2)],
        value: value[1],
      });
    } else {
      results[results.length - 1].ttl = value[1];
    }
    return results;
  }, []);
};

const msetWithTtl = async (rows: { key: string; value: string; ttl: number }[]) => {
  return rows
    .reduce((acc, { key, value, ttl }) => acc.set(key, value).expire(key, ttl), redis.multi())
    .exec();
};

const smembers = (key) => {
  return redis.smembers(key);
};

const delKeysWithScan = async (pattern: string) =>
  new Promise<void>((resolve, reject) => {
    const stream = redis.scanStream({ match: pattern, count: 100 });
    const promises: Promise<any>[] = [];
    let pipeline = redis.pipeline();
    let batchSize = 0;

    stream.on('data', (keys) =>
      keys.forEach((key) => {
        pipeline.del(key);
        batchSize++;

        if (batchSize > 100) {
          promises.push(
            pipeline.exec().catch((err) => {
              logger.error('delKeysWithScan pipelineError: %o', {
                error: err.message,
                stack: err.stack,
                pattern,
              });
              throw err;
            }),
          );

          batchSize = 0;
          pipeline = redis.pipeline();
        }
      }),
    );
    stream.on('end', () => {
      if (batchSize > 0) {
        promises.push(pipeline.exec());
      }

      Promise.all(promises)
        .then(() => resolve())
        .catch(reject);
    });
    stream.on('error', reject);
  }).catch((err) => {
    logger.error('delKeysWithScan error: %o', { error: err.message, stack: err.stack, pattern });
    throw err;
  });

const delUserCache = async (userId) => {
  if (inited()) {
    const lockKey = `delUserCache:${userId}:lock`;
    const result = await setexnx(lockKey, '', 10);
    if (result === null) {
      return;
    }

    try {
      const start = Date.now();
      logger.info(`redisOperation:delUserCache ${userId} started`);
      await delKeysWithScan(`frontend:${userId}:campaign:*`);
      logger.info({ took: Date.now() - start, userId }, 'redisOperation:delUserCache %d', userId);
    } catch (e: any) {
      logger.error(`redisOperation:delUserCache ${userId} error: ${e.message}`);
      throw e;
    } finally {
      logger.info(`redisOperation:delUserCache ${userId} removeing key: ${lockKey}`);
      await del(lockKey);
    }
  } else {
    logger.warn('no active redis connection for cache delete, user:', userId);
  }
};

const deleteBullQueueLeftover = async () => {
  if (!inited()) {
    logger.warn('no active redis connection for bull queue cleanup');
  }

  const start = Date.now();
  await delKeysWithScan('bull:failed-sub:*');
  logger.info(`redisOperation:deleteBullQueueLeftover`, { took: Date.now() - start });
};

export const getAndDellPageViewsAndUserVisitors = async () => {
  const pageViewKey = getPageViewsKey();
  const usedVisitorKey = getUsedVisitorKey();
  const result = await redis
    .multi()
    .hgetall(pageViewKey)
    .hgetall(usedVisitorKey)
    .del(pageViewKey)
    .del(usedVisitorKey)
    .exec();
  return {
    pageViews: result[0][1],
    usedVisitor: result[1][1],
  };
};

export const increasePageViews = async (accountId: number, by: number = 1) => {
  const key = getPageViewsKey();
  return redis.hincrby(key, accountId.toString(), by);
};

export const increaseUsedVisitor = async (accountId: number, by: number = 1) => {
  const key = getUsedVisitorKey();
  return redis.hincrby(key, accountId.toString(), by);
};

export const isUniqueDigicelVisitor = async (clientId: string) => {
  const key = getUniqueDigicelKey();
  return redis.hexists(key, clientId);
};

export const setUniqueDigicelVisitor = async (clientId: string) => {
  const key = getUniqueDigicelKey();
  return redis.hset(key, clientId, 1);
};

export const isUniqueTurbodietaVisitor = async (clientId: string) => {
  const key = getUniqueTurbodietaKey();
  return redis.hexists(key, clientId);
};

export const setUniqueTurbodietaVisitor = async (clientId: string) => {
  const key = getUniqueTurbodietaKey();
  return redis.hset(key, clientId, 1);
};

export const isUniquePremiumShop24Visitor = async (clientId: string) => {
  const key = getUniquePremiumShop24Key();
  return redis.hexists(key, clientId);
};
export const setUniquePremiumShop24Visitor = async (clientId: string) => {
  const key = getUniquePremiumShop24Key();
  return redis.hset(key, clientId, 1);
};

export const isUniqueTroisSoixanteSubVisitor = async (clientId: string) => {
  const key = getTroisSoixanteSubKey();
  return redis.hexists(key, clientId);
};

export const setUniqueTroisSoixanteSubVisitor = async (clientId: string) => {
  const key = getTroisSoixanteSubKey();
  return redis.hset(key, clientId, 1);
};

export const isUniqueRoadRecordVisitor = async (clientId: string) => {
  const key = getRoadRecordKey();
  return redis.hexists(key, clientId);
};

export const setUniqueRoadRecordVisitor = async (clientId: string) => {
  const key = getRoadRecordKey();
  return redis.hset(key, clientId, 1);
};

const isUniqueVisitor = async (databaseId: number, clientId: string) => {
  const key = getUniqueVisitorKey(databaseId);
  return redis.hexists(key, clientId);
};

const setUniqueVisitor = async (databaseId: number, clientId: string) => {
  const key = getUniqueVisitorKey(databaseId);
  return redis.hset(key, clientId, 1);
};

/**
 * @param identifier
 * @param expiration - in seconds
 */
export const addConversionValidation = async (identifier: string, expiration: number) => {
  const key = getConversionValidationKey(identifier);
  return redis.setex(key, expiration, 1);
};

export const hasConversionValidation = async (identifier: string) => {
  const key = getConversionValidationKey(identifier);
  return redis.get(key);
};

export const incrementStatistics = ({
  key,
  period,
  accountId,
  variantId,
  campaignId,
  device,
  experimentGroupId,
  isControlVariant,
}) => {
  const hashKey = `${key}:${accountId}:${campaignId}:${variantId}:${device}:${period}:${
    experimentGroupId ?? ''
  }:${isControlVariant ?? ''}`;

  return redis.hincrby(getStatisticsKey(), hashKey, 1);
};

export const parseStatistics = (obj) => {
  const hash = Object.keys(obj)[0];
  const value = obj[hash];
  const [key, accountId, campaignId, variantId, device, period] = hash.split(':');

  return {
    key,
    accountId,
    campaignId,
    variantId,
    device,
    period,
    value,
  };
};

const getStatistics = () => {
  return redis.hgetall(getStatisticsKey());
};

const clearStatistics = () => {
  return redis.del(getStatisticsKey());
};

const setShopAttributes = (shopType, shopName, key, value, attributes) => {
  // redis supports expiration only on key levels so can't use hash here.
  return redis.setex(getShopAttributesKey(shopType, shopName, key, value), ONE_DAY, attributes);
};

const getShopAttributes = (shopType, shopName, key, value) => {
  return redis.get(getShopAttributesKey(shopType, shopName, key, value));
};

const getPreviewCache = (accountId, campaignId, variantId) => {
  return redis.get(getPreviewCacheKey(accountId, campaignId, variantId));
};

const savePreviewCache = (accountId, campaignId, variantId, template) => {
  const CACHE_TIME = 86400; // 24 hours
  return redis.setex(
    getPreviewCacheKey(accountId, campaignId, variantId),
    CACHE_TIME,
    JSON.stringify(template),
  );
};

const getPreviewV3Cache = (accountId, variantId) => {
  return redis.get(getPreviewV3CacheKey(accountId, variantId));
};

const savePreviewV3Cache = (accountId, variantId, campaign) => {
  const CACHE_TIME = 86400; // 24 hours
  return redis.setex(
    getPreviewV3CacheKey(accountId, variantId),
    CACHE_TIME,
    JSON.stringify(campaign),
  );
};

const deletePreviewV3Cache = (accountId, variantId) => {
  return redis.del(getPreviewV3CacheKey(accountId, variantId));
};

export const client = redis as RedisClient;

export default {
  del,
  mdel,
  delUserCache,
  get,
  hget,
  hset,
  increasePageViews,
  increaseUsedVisitor,
  isUniqueDigicelVisitor,
  setUniqueDigicelVisitor,
  isUniqueTurbodietaVisitor,
  setUniqueTurbodietaVisitor,
  isUniqueTroisSoixanteSubVisitor,
  setUniqueTroisSoixanteSubVisitor,
  isUniquePremiumShop24Visitor,
  setUniquePremiumShop24Visitor,
  isUniqueRoadRecordVisitor,
  setUniqueRoadRecordVisitor,
  isUniqueVisitor,
  setUniqueVisitor,
  incrementStatistics,
  getStatistics,
  clearStatistics,
  mget,
  mgetWithTtl,
  msetWithTtl,
  mset,
  smembers,
  redis,
  set,
  setexnx,
  setex,
  setnx,
  multiSetnxTtl,
  expire,
  ttl,
  deleteBullQueueLeftover,
  setShopAttributes,
  getShopAttributes,
  parseStatistics,
  getPreviewCache,
  getPreviewV3Cache,
  savePreviewCache,
  savePreviewV3Cache,
  deletePreviewV3Cache,
};
