import requests, json, subprocess, os, time, sys

DOKER_LOGIN = sys.argv[1]
DOCKER_PWD = sys.argv[2]
DOCKER_REGISTERY_HOST = sys.argv[3]
IMG = sys.argv[4]
DO_TOKEN = sys.argv[5]

REQ_TIMEOUT = 10 # seconds
HC_TRIES = 5

# vars
hed = {'Authorization': 'Bearer ' + DO_TOKEN}
url_base = 'https://api.digitalocean.com/v2/'
deploy_tag = 'om-live-frontend'
traffic_tag = 'om-live-frontend-traffic'

def wait_pid(pid):
  _, systemstatus = os.waitpid(pid, 0)
  if systemstatus != 0:
    print('exit status check failed')
    exit(1)

def validate_response_status(response):
  s_c = response.status_code
  if s_c >= 205:
    print('status code check failed', s_c)
    exit(1)

def healtcheck(ip):
  url = "http://{}/health".format(ip)
  try:
    r = requests.get(url, timeout=REQ_TIMEOUT)
    s_c = r.status_code
  except:
    s_c = 500
  i = 0
  while s_c != 200 and i <= HC_TRIES:
    print('try healthcheck', i)
    time.sleep(5)
    try:
      r = requests.get(url, timeout=REQ_TIMEOUT)
      s_c = r.status_code
    except:
      s_c = 500
    i = i + 1

  if i < HC_TRIES:
    print('healthcheck passed')
  else:
    print('healthcheck failed')
    exit(1)

if (len(sys.argv) != 6):
  print('5 paramters are required to deploy docker username, docker password, registry host, img, do token')
  exit(1)

# get deploy targets
droplet_list_url = url_base + 'droplets?tag_name=' + deploy_tag
response = requests.get(droplet_list_url, headers=hed, timeout=REQ_TIMEOUT)
doplets = response.json()['droplets']
deploy_targets = []

for droplet in doplets:
  name = droplet['name']
  id = droplet['id']
  # find public ip
  ip = next(x for x in droplet['networks']['v4'] if x['type'] == 'public')['ip_address']
  deploy_targets.append({'name': name, 'id': id, 'ip': ip})

os.chdir('deploy')
print('deploy targets:', deploy_targets)
COMMAND = r"""sed "s|IMG_TAG|{}|g" docker-compose_tpl.yml > docker-compose.yml""".format(IMG)
print('sed docker-compose_tpl', COMMAND)
p = subprocess.Popen(COMMAND, shell=True)
wait_pid(p.pid)

# deploy

tag_url = url_base + "tags/{}/resources".format(traffic_tag)
for idx, deploy_target in enumerate(deploy_targets):
  print('')
  print('==============================')
  print('deploying to:', deploy_target)
  print('==============================')
  print('==', idx + 1, '/', len(deploy_targets))
  print('==')

  tag_payload = {
    'resources': [{
      'resource_id': str(deploy_target['id']),
      'resource_type': 'droplet'
    }]
  }
  print('start untagging', deploy_target['name'], traffic_tag)
  r = requests.delete(tag_url, json=tag_payload, headers=hed, timeout=REQ_TIMEOUT)
  validate_response_status(r)
  time.sleep(5)

  print('copy compose and env file')
  p = subprocess.Popen([
    "scp",
    "-o StrictHostKeyChecking=no",
    "-o UserKnownHostsFile=/dev/null",
    "environment.env",
    "docker-compose.yml",
    "root@{}:~".format(deploy_target['ip'])
  ])
  wait_pid(p.pid)

  print('update and start compose')
  p = subprocess.Popen([
    "ssh",
    "-o StrictHostKeyChecking=no",
    "-o UserKnownHostsFile=/dev/null",
    "root@{}".format(deploy_target['ip']),
    "docker login -u {} -p {} {}; docker-compose pull; docker-compose up -d > /dev/null"
      .format(DOKER_LOGIN, DOCKER_PWD, DOCKER_REGISTERY_HOST)
  ])
  wait_pid(p.pid)
  healtcheck(deploy_target['ip'])

  print('start tagging', deploy_target['name'], traffic_tag)
  r = requests.post(tag_url, json=tag_payload, headers=hed, timeout=REQ_TIMEOUT)
  validate_response_status(r)
  time.sleep(5)

print('Deploy finished!')
