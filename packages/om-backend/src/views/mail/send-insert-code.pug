extends layout-v2

block mail-content
  p(style="font-weight: bold") #{ translator.translate('send-insert-code.mail.sentOut') }
  div(style="margin: 10px 0")
    div #{ login.firstName + " " + login.lastName }
    div #{ login.email }
  //- p(style="font-size: 18px; font-weight: bold;") #{translator.translate('send-insert-code.mail.greeting', {name: name})}
  //- p #{translator.translate('send-insert-code.mail.instruction')}
  //- code(style="font-size: 13px;").
  //-   &lt;script type=&quot;text/javascript&quot;&gt;
  //-     (function(e,a){
  //-         var t,r=e.getElementsByTagName(&quot;head&quot;)[0],c=e.location.protocol;
  //-         t=e.createElement(&quot;script&quot;);t.type=&quot;text/javascript&quot;;
  //-         t.charset=&quot;utf-8&quot;;t.async=!0;t.defer=!0;
  //-         t.src=c+&quot;//front.optimonk.com/public/&quot;+a+&quot;/js/preload.js&quot;;r.appendChild(t);
  //-     })(document,&quot;#{userId}&quot;);
  //-   &lt;/script&gt;
  //- p #{translator.translate('send-insert-code.mail.note')}
  pre #{ note }

block footer
