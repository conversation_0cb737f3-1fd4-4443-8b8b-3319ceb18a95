const express = require('express');

const router = express.Router();
const ONE_MINUTE = 60 * 1000;

const ActOn = require('../services/integrations/actOn');
const CleverReach = require('../services/integrations/cleverReachOAuth');
const Keap = require('../services/integrations/keap');
const MailUp = require('../services/integrations/mailUp');
const Marketo = require('../services/integrations/marketo');
const Salesforce = require('../services/integrations/salesforce');

router.use(async (req, res, next) => {
  if (req.query.token === process.env.cron_token) {
    req.setTimeout(10 * ONE_MINUTE);
    next();
  } else {
    res.sendStatus(401);
  }
});

const oAuthIntegrations = {
  actOn: true,
  cleverReach: true,
  keap: true,
  mailUp: true,
  marketo: true,
  salesforce: true,
};

router.post('/', async (req, res) => {
  const { settings } = req.body;
  const globalSettings = settings[0].integrations;
  let refreshed = false;

  globalSettings.forEach(async (globalSetting) => {
    if (oAuthIntegrations[globalSetting.type]) {
      let adapter;

      if (globalSetting.type === 'actOn') {
        adapter = new ActOn(globalSetting.data);
      } else if (globalSetting.type === 'cleverReach') {
        adapter = new CleverReach(globalSetting.data);
      } else if (globalSetting.type === 'keap') {
        adapter = new Keap(globalSetting.data);
      } else if (globalSetting.type === 'mailUp') {
        adapter = new MailUp(globalSetting.data);
      } else if (globalSetting.type === 'marketo') {
        adapter = new Marketo(globalSetting.data);
      } else if (globalSetting.type === 'salesforce') {
        adapter = new Salesforce(globalSetting.data);
        refreshed = (await adapter.ping()) || refreshed;
      }

      if (!adapter) {
        return;
      }

      if (globalSetting.type !== 'salesforce') {
        refreshed = refreshed || (await adapter.isTokenGood());
      }
    }
  });

  res.json({ refreshed });
});

module.exports = router;
