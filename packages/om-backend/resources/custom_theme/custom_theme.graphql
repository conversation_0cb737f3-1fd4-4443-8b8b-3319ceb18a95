extend type Query {
  getCustomThemeElementStyle(theme: String!): <PERSON><PERSON><PERSON>
  getCustomThemeThemeKit(themeKitName: String, themeKitId: String): JSON
  getCustomThemes(id: ID, resolveMainTemplate: Boolean): JSON
  getCustomThemeCount(name: String): Int
  getThemesTemplate(names: [String!]!, types: [String], goals: [String], contents: [String]): JSON
  getMasterTheme(themeKitName: String!): JSON
}

extend type Mutation {
  upsertCustomTheme(
    id: ID
    name: String!
    sourceTheme: ID
    sourceThemeName: String
    themeKit: JSON!
    hidden: Boolean
    logo: J<PERSON><PERSON>
    createdFromWizard: Boolean
  ): JSON
  updateCustomThemeElementStyle(
    theme: String!
    type: String!
    version: String!
    userCreated: Boolean!
    value: JSON!
  ): JSON
  updateThemeKit(name: String!, oldName: String!, value: JSON!): J<PERSON><PERSON>
  changeCustomThemeStyleName(
    oldName: String!
    newName: String!
    type: String!
    themeKit: String!
  ): JSON
  deleteCustomThemeStyle(name: String!, type: String!, themeKit: String!): JSON
  renameCustomThemeName(id: ID!, name: String!): Response
  archiveCustomTheme(themeId: ID!): Response
}
