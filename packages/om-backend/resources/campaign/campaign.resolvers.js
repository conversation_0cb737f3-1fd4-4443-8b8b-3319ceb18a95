const randomstring = require('randomstring');
const _get = require('lodash.get');
const _isEmpty = require('lodash.isempty');
const moment = require('moment');

const { applyCustomStyleToTemplate } = require('@om/custom-theme-styles/src/index');
const {
  replaceImages,
  logoTypeWhitelist,
  getNameFromURL,
} = require('@om/template-properties/src/imageReplace');
const { isSameThemekit, isSameElementStyles } = require('@om/themekit');

const { logoHide } = require('@om/template-properties/src/logoHide');
const { setIntegrationBindingSuggestions } = require('@om/integrations');
const {
  model,
  dynamicContentCampaignModel,
  ObjectId,
  DYNAMIC_CONTENT_TYPE,
} = require('./campaign.model');
const { model: AccountModel } = require('../account/account.model');
const { model: templateModel } = require('../template/template.model');
const { model: VariantTemplateModel } = require('./variantTemplate.model');
const { userModel: userImageModel } = require('../image/image.model');
const { model: FieldModel, masterModel: fieldMasterModel } = require('../field/field.model');
const { model: LoginModel } = require('../login/login.model');
const { model: UseCaseModel } = require('../useCase/useCase.model');
const { model: ExperienceModel } = require('../experience/experience.model');
const { model: CustomThemeModel } = require('../custom_theme/custom_theme.model');
const { builtInFields } = require('../../util/util');

const { FEATURES, isFeatureEnabled } = require(`../../util/feature`);
const { campaignHasCoupon } = require('../../helpers/couponCode');

const { encrypt, decrypt } = require('../../services/mail/crypto');
const { uploadBase64CropImages } = require('../../services/imageService');

const { getActiveCampaignsUsingFeatures } = require('../../helpers/downgrade');
const jetfabric = require('../../services/jetfabric');

const { decodeToken, signToken, addAuthCookie } = require('../../util/jwt');
const { copyMasterImages } = require('../../services/s3');
const { regenerateTemplateIds, removePageByTitle } = require('../../services/campaignUtil');
const sendInvitationEmail = require('../../services/mail/notifyMeInvitation');
const omAdapter = require('../../services/omAdapter');
const heapAnalytics = require('../../services/heapAnalytics');
const EventDispatcher = require('../../services/EventDispatcher');
const redis = require('../../services/ioRedisAdapter');
const { addCacheLoadJob } = require('../../services/frontendCacheLoadQueue');
const {
  copyCouponConfigConfigsForCampaign,
  removeCouponConfigsForVariant,
} = require('../../services/couponConfig');
const { saveFixedCouponToCache } = require('../../services/omFrontendAdapter');
const { getAuthData } = require('../../helpers/getShopAuthData');
const { isShopifyDomain, purifyDomain } = require('../../util/domainHelper');
const { getCampaignAlerts } = require('./queries/getCampaignAlert');
const { getActiveChanges } = require('../dynamic_content/queries/activeChanges');
const DefaultIntegrationHandler = require('../../services/defaultIntegration/factory');
const RecommendedIntegrationManager = require('../../services/recommendedIntegration/RecommendedIntegrationManager');
const RecommendationStateResolver = require('../../services/recommendedIntegration/RecommendationStateResolver');
const { updateAccountCampaignInfo } = require('../../helpers/account/campaigns');
const { setUserProfileKey } = require('../account/helpers');
const {
  isPrettyBackground,
  updateVariantTypes,
  copyVariant: executeCopyVariantOperation,
  copyDynamicContentVariant,
  updateCampaignExperiencesRules,
  getThemeType,
  getCampaignType,
  setRunningSABTests,
  getIncreasedCampaignCount,
  findDomainByUrl,
  getDynamicContentChangesFromCache,
  FEATURE_MODE_SAB,
  FEATURE_MODE_DC,
  getDCNamePrefix,
  updateUserCaches,
  getVariantTemplate,
  createChangelogs,
} = require('./helper');
const { restoreCampaigns } = require('./mutations/restoreCampaigns');
const { archiveCampaigns } = require('./mutations/archiveCampaigns');
const {
  removeCampaignFromExperiment,
  resolveExperimentName,
  getRequestedFields,
  getAllCampaignFields,
} = require('../../helpers/campaigns');
const smartHeadlineHelper = require('../../helpers/smartHeadline');
const allCampaigns = require('./queries/allCampaigns');
const { dynamicContentModel } = require('../dynamic_content/dynamic_content.model');
const { queueCampaignChange } = require('../../services/queue/changeLogQueue');
const { campaignChangeTypes } = require('../changeLog/common');
const { queueVariantTypeUpdate } = require('../../services/queue/variantTypesQueue');
const OpenAIAdapter = require('../../services/AIAdapters/OpenAI');
const { createControlVariant } = require('./mutations/createControlVariant');
const { deleteControlVariant } = require('./mutations/deleteControlVariant');
const TemplateTranslator = require('../../services/templateTranslator/TemplateTranslator');
const { copyCampaignFromAnotherAccount } = require('./mutations/copyCampaignFromAnotherAccount');
const { copyEvaluations } = require('../../services/smartABTest/evaluation/mongoHelper');
const {
  SmartABTestEvaluator,
} = require('../../services/smartABTest/evaluation/SmartABTestEvaluator');
const { allVariantsWithPeriodBasedStats } = require('./queries/allVariants');
const { addAlertsToCampaigns } = require('../../helpers/userAlertV2');
const { checkMissingBindings } = require('../../helpers/campaignIntegrations');

const OM_ADMIN_URL = process.env.om_admin_url;
const OM_SALES_LANDING = {
  en: '//landing.optimonk.com/powered-by-optimonk',
  hu: '//landing.optimonk.hu/powered-by-optimonk',
};
const SMS_PAGE_TITLE = 'SMS';

const handleLogoChange = async (databaseId, customTheme, template) => {
  if (!customTheme.logo.original || customTheme.logo.current === customTheme.logo.original) return;

  let name;
  let image;

  const themeKitLogoHasTimestamp = /_\d{13}\./.test(customTheme.logo.current);

  if (themeKitLogoHasTimestamp) {
    name = getNameFromURL(customTheme.logo.current, true);
    image = await userImageModel.findOne({
      databaseId,
      url: { $regex: name },
    });
  } else {
    name = getNameFromURL(customTheme.logo.current);
    image = await userImageModel.findOne({
      databaseId,
      url: new RegExp(
        `${name?.replace?.(/\.((jpeg|jpg|jpeg-2000|png|svg|gif|tiff|webp))$/, '_\\d{13}.$1')}`,
      ),
    });
  }

  if (name && image) {
    replaceImages({
      template,
      imageData: {
        currentImage: customTheme?.logo?.original,
        selectedImage: {
          id: image?._id,
          url: image?.url,
          width: image?.width,
          height: image?.height,
        },
      },
      whitelist: logoTypeWhitelist,
    });
    template.images.push({
      _id: image._id.toString(),
      name: image.name,
      url: image.url,
      height: image.height,
      width: image.width,
      __typename: 'Image',
    });
  } else if (!name && !customTheme?.logo?.current && customTheme?.logo?.original) {
    logoHide(template.elements, customTheme.logo.original);
  }

  return template;
};

const variant = async (_, { _id }, { userId, loginId, log }) => {
  log.info({ message: 'loading variant for user', userId, variantId: _id });
  const CampaignModel = model(userId);
  const [campaign, account] = await Promise.all([
    CampaignModel.findOne(
      { 'variants._id': ObjectId(_id) },
      {
        id: 1,
        name: 1,
        domainId: 1,
        device: 1,
        'variants.$': 1,
        lang: 1,
        locale: 1,
        settings: 1,
        templateName: 1,
        templateId: 1,
        version: 1,
        isNewCampaign: 1,
      },
    ),
    AccountModel.findOne(
      { databaseId: userId },
      {
        type: 1,
        'settings.domains': 1,
        'settings.hasPoweredByLinkDisabled': 1,
        users: 1,
        billing: 1,
        features: 1,
      },
    ),
  ]);
  // no loginId when taking screenshoot
  if (!loginId) loginId = _get(account, 'users.0.loginId');
  const login = await LoginModel.findOne({ _id: loginId }, { locale: 1 });

  let result = {};
  // campaign settings
  if (campaign) {
    const templateQuery = await templateModel.findOne(
      { _id: ObjectId(campaign.templateId) },
      { universal: 1, theme: 1 },
    );
    const universal = templateQuery?.universal;
    const theme = templateQuery?.theme;
    const variant = campaign.variants[0];

    if (isFeatureEnabled(account, FEATURES.VARIANT_TEMPLATE_MIGRATED)) {
      const variantTemplate = await VariantTemplateModel.findOne({
        databaseId: userId,
        campaignId: campaign.id,
        variantId: _id,
      });

      if (variantTemplate) {
        variant.template = variantTemplate.template;
      }
    }

    const isNewCampaign = campaign?.isNewCampaign;
    const locale = login && login.locale ? login.locale : 'en';
    if (variant?.template?.theme) {
      variant.template.theme = theme;
    }

    const isPoweredByDisabledByPackage =
      account.type === 'normal' &&
      ['ESSENTIAL', 'GROWTH', 'PREMIUM', 'MASTER', 'DEMO'].some((e) =>
        account.billing.package.startsWith(e),
      );
    log.info({ isPoweredByDisabledByPackage });
    let isPoweredByEnabled = true;
    if (!isPoweredByDisabledByPackage) {
      isPoweredByEnabled = !(account.type === 'sub'
        ? await AccountModel.isPoweredByLinkDisabledInOwnerAccount(account._id)
        : _get(account, 'settings.hasPoweredByLinkDisabled') === true);
    } else {
      isPoweredByEnabled = false;
    }

    log.info('campaign found: %o', {
      campaignId: campaign._id.toString(),
      userId,
      poweredByDisabled: !isPoweredByEnabled,
    });
    let domain = null;
    if (account) {
      let domains = _get(account, 'settings.domains');
      let found = domains.find(
        (d) => campaign.domainId && d._id.toString() === campaign.domainId.toString(),
      );
      if (found) domain = found.domain;
    } else {
      domain = campaign.domain;
    }

    let poweredBy = {
      visible: isPoweredByEnabled,
      text: 'Made with ♥️ by OptiMonk',
      link: `${OM_SALES_LANDING[locale]}/?utm_source=link&utm_medium=optimonk_popup&utm_campaign=${userId}&domain=${domain}`,
    };

    result = {
      campaignInnerId: campaign._id,
      campaignId: campaign.id,
      campaignName: campaign.name,
      templateName: campaign.templateName,
      device: campaign.device,
      domain,
      template: JSON.stringify(variant.template),
      variantName: variant.name,
      variantId: variant.id,
      omUrl: OM_ADMIN_URL,
      lang: variant.locale || locale,
      poweredBy,
      databaseId: userId,
      universal,
      theme,
      version: campaign.version,
      isNewCampaign,
    };
  }
  return result;
};

const addNormalVariant = async (campaign, userId, login) => {
  const { _id } = campaign;
  const CampaignModel = model(userId);
  const customThemes = await CustomThemeModel.find({
    $or: [
      { databaseId: 44, sourceTheme: { $exists: false } },
      { databaseId: userId, sourceTheme: { $exists: true } },
    ],
  });

  const { variantCount, templates, id } = campaign;

  let template = templates[0];
  const isTemplateThemeKit = template?.themeKit?.name;

  if (isTemplateThemeKit) {
    let sourceCampaign;
    const account = await AccountModel.findOne({ databaseId: userId }, { features: 1 });
    if (isFeatureEnabled(account, FEATURES.VARIANT_TEMPLATE_MIGRATED)) {
      const variantTemplates = await VariantTemplateModel.find({
        campaignId: campaign.id,
        databaseId: userId,
      });

      sourceCampaign = {
        variants: variantTemplates.map(({ template }) => ({
          template: {
            theme: template.theme,
            themeKit: {
              name: template.themeKit.name,
            },
          },
        })),
      };
    } else {
      sourceCampaign = await CampaignModel.findOne(
        { _id },
        { 'variants.template.themeKit.name': 1, 'variants.template.theme': 1 },
      );
    }
    const themeName = _get(sourceCampaign, 'variants.0.template.theme');
    const themeKitName = _get(sourceCampaign, 'variants.0.template.themeKit.name', themeName);

    if (themeKitName) {
      const baseThemes = customThemes.filter(({ sourceTheme }) => !sourceTheme);
      const userThemes = customThemes
        .filter(({ sourceTheme }) => !!sourceTheme)
        .map((theme) => {
          const sourceTheme = baseThemes.find(({ _id }) => _id.equals(theme.sourceTheme));
          return {
            ...theme.toObject(),
            sourceTheme,
          };
        })
        .filter(({ sourceTheme }) => !!sourceTheme);
      const applicableTheme = userThemes.find(({ name, sourceTheme }) => {
        return name === themeKitName || sourceTheme.name === themeKitName;
      });
      if (applicableTheme) applyCustomStyleToTemplate(template, applicableTheme);
    }
  }

  const newVariantId = ObjectId();
  await CampaignModel.update(
    { _id },
    {
      $push: {
        variants: {
          _id: newVariantId,
          name: `${login.locale === 'hu' ? 'Variáns' : 'Variant'} ${variantCount + 1}`,
          template: regenerateTemplateIds(template),
          status: 'inactive',
          previewGenerated: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      },
    },
  );
  const token = await signToken({ userId }, { expiresIn: 300 });
  omAdapter.uploadHtmlAndPreviewsForVariant({
    model: CampaignModel,
    campaignId: id,
    variantId: newVariantId,
    token,
    userId,
  });
  EventDispatcher.emit(EventDispatcher.events.CREATIVE_CREATE, {
    databaseId: userId,
  });

  queueVariantTypeUpdate(userId, id, newVariantId);

  return true;
};

const addDynamicContentVariant = async (campaign, userId, login) => {
  const { _id, id } = campaign;
  const CampaignModel = model(userId);
  const newVariantId = ObjectId();

  await CampaignModel.updateOne(
    { _id },
    {
      $push: {
        variants: {
          _id: newVariantId,
          name: `${login.locale === 'hu' ? 'Variáns' : 'Variant'} ${campaign.variantCount + 1}`,
          status: 'inactive',
          changes: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      },
    },
  );

  queueVariantTypeUpdate(userId, id, newVariantId);

  return true;
};

const newVariant = async (_, { input }, { userId, loginId }) => {
  const CampaignModel = model(userId);
  const _id = await idToMongoId(userId, input._id);
  const login = await LoginModel.findOne({ _id: ObjectId(loginId) }, { locale: 1 }).lean();

  const [campaign] = await CampaignModel.aggregate([
    { $match: { _id } },
    {
      $lookup: {
        from: 'master_templates',
        localField: 'templateId',
        foreignField: '_id',
        as: 'template',
      },
    },
    {
      $project: {
        variants: 1,
        variantCount: { $size: '$variants' },
        templates: '$template.template',
        id: 1,
        type: 1,
        universal: '$template.universal',
        theme: '$template.theme',
      },
    },
  ]);

  if (campaign.type === 'dynamic_content') {
    return addDynamicContentVariant(campaign, userId, login);
  }

  return addNormalVariant(campaign, userId, login);
};

const copyVariant = async (_, { variantId, templateType, experience }, userContext) => {
  const { userId, superadmin, superAdminName, superAdminEmail, role, loginId } = userContext;
  let result;
  if (templateType === 'dynamic_content') {
    result = await copyDynamicContentVariant({ variantId, userId });
  } else {
    result = await executeCopyVariantOperation({ variantId, userId });
  }

  const copyId = result?.variant?._id;
  if (experience && copyId) {
    const experienceId = ObjectId(experience);
    await ExperienceModel.updateOne({ _id: experienceId }, { $push: { variants: copyId } });
  }

  queueCampaignChange({
    changeContext: {
      campaignId: result?.campaign?._id,
      variantId: copyId,
    },
    changeType: campaignChangeTypes.VARIANT_CREATED,
    userContext: { userId, superadmin, superAdminName, superAdminEmail, role, loginId },
  });

  return true;
};

const renameVariant = async (_, { input }, { userId }) => {
  const { _id, name } = input;
  const CampaignModel = model(userId);
  await CampaignModel.updateOne({ 'variants._id': _id }, { $set: { 'variants.$.name': name } });
  updateUserCaches(userId);
  return true;
};

const templateHasFollowup = (elements) => {
  return elements.some((e) => e.type === 'OmCoupon' && e.data.coupon.type === 'followup');
};

const handleFollowUpCreated = async ({ elements, userId, campaignId }) => {
  if (templateHasFollowup(elements)) {
    heapAnalytics.trackFollowupCampaignCreated(userId, { id: campaignId });
    await AccountModel.updateOne(
      { databaseId: userId, 'profile.firstFollowupCreated': { $exists: false } },
      { $set: { 'profile.firstFollowupCreated': new Date() } },
    );
  }
};

const saveCouponToCache = async ({ domain, account, elements }, { log }) => {
  const accountData = await AccountModel.findOne({ databaseId: account }, { 'settings.shops': 1 });

  const isShopify = isShopifyDomain(domain, accountData.settings?.shops);

  if (!isShopify) {
    return;
  }

  const fixedCodes = elements
    .filter((e) => e.type === 'OmCoupon' && e.data.coupon.type === 'fixed')
    .map((fixedCoupon) => fixedCoupon.data.coupon.fixedCoupon);

  if (fixedCodes.length) {
    const authData = await getAuthData({
      databaseId: account,
      type: 'shopify',
      domain: `${domain}`.replace('www.', ''),
    });

    if (authData) {
      try {
        await saveFixedCouponToCache({
          account,
          discountCodes: fixedCodes,
          shopId: `${authData.shopName}.myshopify.com`,
        });
      } catch (e) {
        log.warn({
          err: e,
          msg: `Could not create discount cache for discountCodes ${fixedCodes.join(', ')}`,
        });
      }
    }
  }
};

const updateVariant = async (_, { input }, userContext) => {
  const { userId, log, superadmin, superAdminName, superAdminEmail, role, loginId } = userContext;
  const { _id, name, template } = input;
  const CampaignModel = model(userId);
  const templateObj = JSON.parse(template);

  if (templateObj?.themeKit?.id) {
    templateObj.themeKit.id = ObjectId(templateObj.themeKit.id);
  }

  try {
    await smartHeadlineHelper.trackSmartHeadlineOnUpdate(userId, _id, templateObj);
  } catch (e) {
    log.warn({
      message: 'Error during smart headline update reporting',
      databaseId: userId,
      variantId: _id,
      errorMessage: e.message,
    });
  }

  const dbFields = await FieldModel.find({ databaseId: userId }, { customId: 1, name: 1, type: 1 });
  const masterFields = await fieldMasterModel.find({}, { customId: 1, name: 1, type: 1 });
  const allFields = builtInFields.concat(dbFields, masterFields);
  templateObj.inputs = templateObj.inputs
    .map((input) => {
      return allFields.find((db) => db.customId === input.customId);
    })
    .filter((e) => e != null);
  if (templateObj.style.mode === 'sidebar' && templateObj.style.overlay.position === 5) {
    templateObj.style.mode = 'popup';
  }
  await uploadBase64CropImages(userId, _id, templateObj);
  await smartHeadlineHelper.cleanupHeadlinesBy(_id, templateObj);

  const account = await AccountModel.findOne(
    { databaseId: userId },
    { features: 1, settings: 1, databaseId: 1 },
  );
  const existingCampaignFields = await getAllCampaignFields(userId, null, _id);

  let updateObject = {
    'variants.$.updatedAt': new Date(),
    'variants.$.mode': templateObj.style.mode,
    'variants.$.overlayPosition': templateObj.style.overlay.position,
    isNewCampaign: false,
  };
  if (!isFeatureEnabled(account, FEATURES.VARIANT_TEMPLATE_MIGRATED)) {
    updateObject['variants.$.template'] = templateObj;
  }
  if (name) updateObject['variants.$.name'] = name;
  log.info('startUpdateVariant', userId, _id);
  const campaign = await CampaignModel.findOneAndUpdate(
    { 'variants._id': ObjectId(_id) },
    { $set: updateObject },
    { new: true },
  );
  queueCampaignChange({
    changeContext: {
      campaignId: campaign._id,
      variantId: ObjectId(_id),
    },
    changeType: campaignChangeTypes.MODIFIED_VARIANT_DESIGN,
    userContext: { userId, superadmin, superAdminName, superAdminEmail, role, loginId },
  });
  if (!campaign) return false;

  if (isFeatureEnabled(account, FEATURES.VARIANT_TEMPLATE_MIGRATED)) {
    await VariantTemplateModel.updateOne(
      { databaseId: userId, campaignId: campaign.id, variantId: ObjectId(_id) },
      { $set: { template: templateObj } },
    );
  }

  const campaignId = campaign.id;

  await saveCouponToCache(
    { domain: campaign.domain, account: userId, elements: templateObj.elements },
    { log },
  );

  await handleFollowUpCreated({ elements: templateObj.elements, userId, campaignId });

  addCacheLoadJob({ type: 'get-variant', accountId: userId, variantId: _id, campaignId });

  const hasCouponCode = await campaignHasCoupon({
    account,
    databaseId: userId,
    campaignId: campaign.id,
  });

  const token = await signToken({ userId }, { expiresIn: 300 });

  const newCampaignFields = templateObj.inputs.filter(
    (input) =>
      !existingCampaignFields.find((campaignField) => campaignField.customId === input.customId),
  );
  const campaignFields = [...existingCampaignFields, ...newCampaignFields];

  try {
    campaign.settings.integrations = campaign.settings.integrations.map((integration) => {
      integration.bindings = integration.bindings.filter((binding) => {
        if (
          binding.fieldId &&
          binding.externalId &&
          !['campaign_name', 'url', 'variant_name'].includes(binding.fieldId)
        ) {
          const isCouponField = ['coupon_title', 'coupon_code'].indexOf(binding.fieldId) > -1;

          if (isCouponField) {
            return hasCouponCode;
          }

          return campaignFields.find((input) => input && input.customId === binding.fieldId);
        }

        return true;
      });

      const accountIntegration = account.settings.integrations.find(
        ({ _id: integrationId }) => integrationId.toString() === integration.id.toString(),
      );

      if (accountIntegration && integration.bindings.length) {
        setIntegrationBindingSuggestions(
          accountIntegration.type,
          integration.bindings,
          newCampaignFields,
        );
      }

      return integration;
    });
  } catch (e) {
    log.error('binding suggestion error', {
      errorMessage: e.message,
      userId,
      campaignId,
      existingCampaignFields,
      newCampaignFields,
      integrations: campaign.settings.integrations,
    });
  }

  const variantIndex = campaign.variants
    .toObject()
    .findIndex((v) => v._id.toString() === _id.toString());
  if (variantIndex !== -1) {
    campaign.variants[variantIndex].previewGenerated = false;
  }

  campaign.save();
  omAdapter.uploadHtmlAndPreviewsForVariant({
    model: CampaignModel,
    campaignId,
    variantId: _id,
    token,
    userId,
  });
  updateUserCaches(userId);
  log.info('variant updated with screenshot and content');
  EventDispatcher.emit(EventDispatcher.events.CREATIVE_UPDATE, {
    databaseId: userId,
  });

  const defaultIntegration = new DefaultIntegrationHandler(campaign.id, account);
  await defaultIntegration.createDefaultIntegration();

  const DEFAULT_RECOMMENDATION_INTEGRATIONS = ['klaviyo'];

  DEFAULT_RECOMMENDATION_INTEGRATIONS.map(async (integrationType) => {
    const recommendedIntegration = new RecommendedIntegrationManager(
      campaign.id,
      account.databaseId,
      account.features,
      account.settings.integrations,
      integrationType,
    );
    await recommendedIntegration.init();
    await recommendedIntegration.manage();
  });

  await redis.deletePreviewCache(userId, campaignId, _id);
  await redis.deleteVariantCache(userId, campaignId);
  queueVariantTypeUpdate(userId, campaignId, _id);

  await checkMissingBindings(userId, campaign);

  return true;
};

const createVariantFromTemplate = async (_, { token }, { res, log }) => {
  try {
    const decoded = await decodeToken(token, true);
    log.info({ token }, 'decoded token');
    let {
      template: templateName,
      sub: userId,
      caName: campaignName,
      caId: campaignId,
      crId: creativeId,
      crName: creativeName,
      domain,
      locale,
      poweredBySettings: poweredBy,
    } = decoded;
    if (locale === 'hu') templateName += '_hu';
    log.info('createVariantFromTemplate: %o', {
      userId,
      campaignId,
      creativeId,
      templateName,
      locale,
    });
    const account = await AccountModel.findOne(
      { databaseId: userId },
      {
        features: 1,
      },
    );
    const CampaignModel = model(userId);
    let campaign = await CampaignModel.findOne({
      id: campaignId,
      $or: [{ status: { $exists: true } }, { status: { $exists: false } }],
    });
    let template = await templateModel.findOne({ name: templateName });
    if (!template) throw Error('source template not found');
    let newVariant = false;
    if (!campaign) {
      newVariant = true;
      campaign = new CampaignModel({
        templateName,
        name: campaignName,
        id: campaignId,
        domain,
        locale,
        settings: { poweredBy },
        variants: [
          {
            id: creativeId,
            name: creativeName,
            ...(!isFeatureEnabled(account, FEATURES.VARIANT_TEMPLATE_MIGRATED)
              ? { template: template.template }
              : {}),
          },
        ],
      });
      log.info('saving campaign');
      campaign = await campaign.save();
      if (isFeatureEnabled(account, FEATURES.VARIANT_TEMPLATE_MIGRATED)) {
        await VariantTemplateModel.create({
          databaseId: userId,
          campaignId,
          variantId: creativeId,
          template: template.template,
        });
      }
      log.info('start image copy');
      await copyImagesAndUpdateCampaign({
        campaign,
        variantTemplate: template.template,
        account,
        userId,
        templateName,
        log,
      });
      log.info('start field copy');
      await copyFieldsAndUpdateCampaign({
        campaign,
        variantTemplate: template.template,
        account,
        userId,
        templateName,
        log,
      });
      log.info('field copy finished');
    }
    const signedToken = await signToken({ userId });
    addAuthCookie(res, signedToken);
    const variant = campaign.variants.find((v) => v.id === creativeId);
    return {
      newVariant,
      token: signedToken,
      variantId: variant._id,
    };
  } catch (e) {
    log.error(e, 'createVariantFromTemplate');
    throw Error(e.message);
  }
};

const copyFieldsAndUpdateCampaign = async ({
  campaign,
  variantTemplate,
  account,
  userId,
  templateName,
  log,
}) => {
  const result = await Promise.all([
    fieldMasterModel.find({ template: templateName }, { customId: 1, name: 1, type: 1 }),
    FieldModel.find({ databaseId: userId }, { customId: 1, name: 1, type: 1 }),
  ]);
  log.info('###fields from db master ', result[0], 'user', result[1]);
  // user does not have field with this name and type
  const outer = result[0].filter(
    (n) => result[1].find((n2) => n2.name === n.name && n2.type === n.type) === undefined,
  );
  log.info('###outer', outer);
  if (outer.length > 0) {
    const toInsert = outer.map((o) => {
      return {
        name: o.name,
        type: o.type,
        databaseId: userId,
        customId: o.customId,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    });

    await FieldModel.insertMany(toInsert);
  }
  // user has field with this name and type, so we rename in template
  let inter = [];
  result[0].forEach((n) => {
    const found = result[1].find((n2) => n2.name === n.name && n2.type === n.type);
    if (found && n.customId !== found.customId) {
      inter.push({ oldId: n.customId, newId: found.customId });
    }
  });
  log.info('###inter', inter);
  if (inter.length > 0) {
    let template = campaign.variants[0].template;
    if (isFeatureEnabled(account, FEATURES.VARIANT_TEMPLATE_MIGRATED)) {
      template = variantTemplate;
    }
    let templateString = JSON.stringify(template);
    inter.forEach((n) => {
      log.info('replacing in template %o', { oldId: n.oldId, newId: n.newId });
      templateString = templateString.replace(new RegExp(n.oldId, 'g'), n.newId);
    });

    if (isFeatureEnabled(account, FEATURES.VARIANT_TEMPLATE_MIGRATED)) {
      await VariantTemplateModel.updateOne(
        { variantId: campaign.variants[0]._id },
        { $set: { template: JSON.parse(templateString) } },
      );
    } else {
      campaign.variants[0].template = JSON.parse(templateString);
      await campaign.save();
    }
  }
};

const copyImagesAndUpdateCampaign = async ({
  campaign,
  variantTemplate,
  account,
  userId,
  templateName,
  log,
}) => {
  let template = campaign.variants[0].template;
  if (isFeatureEnabled(account, FEATURES.VARIANT_TEMPLATE_MIGRATED)) {
    template = variantTemplate;
  }

  // template has images
  if (template.images.length > 0) {
    const masterImageURLs = template.images.map((i) => i.url.split('/').reverse()[0]);
    const alreadyCopied = await userImageModel.find(
      { databaseId: userId, url: { $in: masterImageURLs.map((url) => new RegExp(`${url}$`)) } },
      { sourceImage: 1, url: 1 },
    );
    const imagesToCopy = template.images.filter((i) => {
      const result = alreadyCopied.find(
        (copied) => copied.sourceImage.toString() === i._id.toString(),
      );
      return result === undefined;
    });
    let copyResult;
    try {
      copyResult = await copyMasterImages(imagesToCopy, {
        variantId: campaign.variants[0]._id,
        userId,
        templateName,
      });
    } catch (err) {
      log.fatal(
        {
          err,
          variantId: campaign.variants[0]._id,
          userId,
          templateName,
        },
        'failed to copy master images',
      );
      throw new Error('Failed to copy master images');
    }
    // console.log('copyResult', copyResult)
    const imagesToSave = imagesToCopy.map((image, index) => {
      return {
        databaseId: userId,
        name: image.name,
        width: image.width,
        height: image.height,
        url: copyResult[index],
        sourceImage: imagesToCopy[index]._id,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    });
    // console.log('imagesToSave', imagesToSave)
    let insertedImages = [];
    if (imagesToSave.length > 0) {
      insertedImages = await userImageModel.insertMany(imagesToSave);
    }
    let templateString = JSON.stringify(template);
    const allUserImages = alreadyCopied.concat(insertedImages);
    template.images.forEach((masterImage, index) => {
      const userImage = allUserImages.find(
        (ui) => ui.sourceImage.toString() === masterImage._id.toString(),
      );
      const { _id: oldId, url: oldUrl } = masterImage;
      const { _id: newId, url: newUrl } = userImage;
      templateString = templateString.replace(new RegExp(oldId, 'g'), newId);
      templateString = templateString.replace(new RegExp(oldUrl, 'g'), newUrl);
    });

    if (isFeatureEnabled(account, FEATURES.VARIANT_TEMPLATE_MIGRATED)) {
      await VariantTemplateModel.updateOne(
        { variantId: campaign.variants[0]._id },
        { $set: { template: JSON.parse(templateString) } },
      );
    } else {
      campaign.variants[0].template = JSON.parse(templateString);
      await campaign.save();
    }
  }
};

const _getDefaultCampaignsRules = async ({
  useCaseId,
  templateType,
  segments,
  isShopifyDomain,
  features,
}) => {
  let useCase = null;
  let shopifySegment = null;
  let nonShopifySegment = null;
  const isFrequencyEnabled = features.includes(FEATURES.FREQUENCY_RULE_V2);

  if (useCaseId) {
    useCase = await UseCaseModel.findOne(
      { _id: useCaseId },
      { events: 1, frontendRules: 1, shopifySegment: 1, nonShopifySegment: 1 },
    );
    if (useCase) {
      shopifySegment = segments.find(({ _id }) => _id.equals(useCase.shopifySegment));
      nonShopifySegment = segments.find(({ _id }) => _id.equals(useCase.nonShopifySegment));
    }
  }

  const segment = isShopifyDomain ? shopifySegment || nonShopifySegment : nonShopifySegment;

  let defaultSettings = {
    events: useCase
      ? useCase.events
      : [
          {
            type: 'exitIntent',
            device: 'desktop_and_mobile',
          },
        ],
    frontendRules: useCase
      ? segment?.frontendRules || useCase.frontendRules
      : [
          {
            type: 'maximumPopupDisplay',
            options: {
              value: 2,
              time: 1,
              unit: 'hour',
            },
          },
        ],
    lastSaved: null,
  };

  if (isFrequencyEnabled) {
    // maximumPopupDisplay is depracated after frequency
    defaultSettings.frontendRules = defaultSettings.frontendRules.filter(
      (rule) => rule.type !== 'maximumPopupDisplay',
    );

    defaultSettings.frequency = {
      showAgain: {
        enabled: true,
        maximumTime: 2,
      },
      delay: {
        enabled: true,
        minimumTime: 1,
        timeUnit: 'hour',
      },
      stopEvents: {
        afterClosed: false,
        afterConverted: true,
      },
    };
  }

  if (templateType === 'embedded') {
    // reset rules and events
    defaultSettings = {
      events: [],
      frontendRules: [],
    };

    if (isFrequencyEnabled) {
      defaultSettings.frequency = {
        showAgain: {
          enabled: false,
          maximumTime: 1,
        },
        delay: {
          enabled: false,
          minimumTime: 1,
          timeUnit: 'second',
        },
        stopEvents: {
          afterClosed: false,
          afterConverted: false,
        },
      };
    }
  }

  if (templateType === 'nanobar') {
    defaultSettings.events = [
      {
        type: 'timed',
        device: 'desktop_and_mobile',
        options: { delay: 1 },
      },
    ];

    defaultSettings.frontendRules = [
      {
        type: 'timeBasedActualPage',
        options: {
          value: 5,
        },
      },
    ];

    if (isFrequencyEnabled) {
      defaultSettings.frequency = {
        showAgain: {
          enabled: false,
          maximumTime: 1,
        },
        delay: {
          enabled: false,
          minimumTime: 1,
          timeUnit: 'second',
        },
        stopEvents: {
          afterClosed: true,
          afterConverted: false,
        },
      };
    }
  }
  return defaultSettings;
};

const _isShopifyDomain = (domainId, userDomains = [], userShops = []) => {
  const domainObject = userDomains.find(({ _id }) => _id.equals(domainId));
  const shopObject = userShops.find(({ domain, live_domain }) =>
    [domain, live_domain].includes(purifyDomain(domainObject.domain)),
  );

  return shopObject && [shopObject.platform, shopObject.type].includes('shopify');
};

const getNewThemeName = async ({ databaseId, locale }) => {
  const userThemesCount = await CustomThemeModel.count({
    databaseId,
    sourceTheme: { $exists: true },
  });

  const localizedName = locale === 'hu' ? 'Saját téma' : 'My theme';
  return `${localizedName} ${userThemesCount + 1}`;
};

const createUserTheme = async ({ customTheme, databaseId, locale, mainColor, secondaryColors }) => {
  const sourceTheme = customTheme._id;
  const isCustomColor = mainColor && !!secondaryColors?.length;
  const newId = new ObjectId();
  const newTheme = new CustomThemeModel({
    ...customTheme.toObject(),
    hidden: true,
    databaseId,
    sourceTheme,
    _id: newId,
  });
  newTheme.isNew = true;
  const newThemeName = await getNewThemeName({ databaseId, locale });

  newTheme.name = newThemeName;
  newTheme.themeKit.id = newId;
  newTheme.themeKit.name = newThemeName;

  if (isCustomColor) {
    newTheme.themeKit.colors.mainColor = mainColor;
    newTheme.themeKit.colors.secondaryColors = secondaryColors;
  }

  await newTheme.save();

  return newTheme;
};

const findExistingCustomTheme = (customThemes, originTheme) => {
  if (!customThemes?.length) return null;
  return customThemes.find(({ themeKit, elementStyles }) =>
    isSameThemekit(themeKit, originTheme.themeKit)
      ? isSameElementStyles(elementStyles, originTheme.elementStyles)
      : false,
  );
};

const setupCustomTheme = async ({ customTheme, databaseId, locale, colors }) => {
  let applicableTheme = customTheme;
  let existingWithSourceTheme = true;
  const [mainColor, ...secondaryColors] = colors ?? [];

  if (!customTheme.sourceTheme) {
    const criteria = {
      databaseId,
      sourceTheme: customTheme._id,
      $or: [{ hidden: true }, { hidden: { $exists: false } }],
    };
    if (mainColor) {
      criteria['themeKit.colors.mainColor'] = mainColor;
    }
    const userCustomThemes = await CustomThemeModel.find(criteria);
    existingWithSourceTheme = findExistingCustomTheme(userCustomThemes, customTheme);
    applicableTheme =
      existingWithSourceTheme ||
      (await createUserTheme({ customTheme, databaseId, locale, mainColor, secondaryColors }));
  }

  return { applicableTheme, isExistingTheme: !!existingWithSourceTheme };
};

const logCampaignCreateToProfile = async (userId, propertyName) => {
  await AccountModel.updateOne(
    { databaseId: userId, [`profile.${propertyName}`]: { $exists: false } },
    { $set: { [`profile.${propertyName}`]: true } },
  );
};

const getHeapBaseThemeName = (template) => {
  if (template.name === 'LOsgBOG2D') return 'Flyer template';
  if (/^Custom/.test(template.name)) return 'From scratch';
  return template.theme;
};

const createNormalCampaign = async (input, userId, loginId, log) => {
  const { templateId, domainId, colors, theme, removeSMSPage } = input;

  const CampaignModel = model(userId);
  let [login, template, campaignCount, account, customTheme] = await Promise.all([
    LoginModel.findOne({ _id: loginId }),
    templateModel.findOne({ _id: templateId }),
    CampaignModel.getMaxCampaignCount(),
    AccountModel.findOne(
      { databaseId: userId },
      {
        'settings.onboarding.campaignCreated': 1,
        'settings.shops': 1,
        'settings.domains': 1,
        'settings.integrations': 1,
        'settings.segments': 1,
        'profile.preferredTemplateLanguage': 1,
        features: 1,
      },
    ),
    theme ? CustomThemeModel.findOne({ _id: ObjectId(theme) }) : Promise.resolve(null),
  ]);

  const {
    settings: {
      domains,
      shops,
      integrations,
      segments,
      onboarding: { campaignCreated },
    },
    features,
    profile,
  } = account;

  campaignCount += 1;

  const name = login.locale === 'hu' ? `Kampány #${campaignCount}` : `Campaign #${campaignCount}`;

  const isShopifyDomain = domainId ? _isShopifyDomain(domainId, domains, shops) : false;
  const defaultSettings = await _getDefaultCampaignsRules({
    useCaseId: template.useCase,
    templateType: template.type,
    segments: segments || [],
    isShopifyDomain,
    features,
  });

  const displayName = template.displayName || template.name;

  let campaign;
  const newVariantId = ObjectId();
  let isExistingTheme;
  const isEmbedded = template.type === 'embedded';
  const preferredTemplateLanguage = profile?.preferredTemplateLanguage;
  let isTranslatedCampaign = false;

  try {
    if (preferredTemplateLanguage && preferredTemplateLanguage !== 'en') {
      const translations = await TemplateTranslator.getTranslations(
        template._id,
        preferredTemplateLanguage,
      );
      if (translations) {
        isTranslatedCampaign = true;
        const { elements, wheelOptions } = TemplateTranslator.replaceTranslations(
          template.template,
          translations,
        );

        template.template.elements = elements;
        template.template.wheelOptions = wheelOptions;
      }
    }

    let generatedTemplate = regenerateTemplateIds(template.template);
    if (removeSMSPage) {
      generatedTemplate = removePageByTitle(template.template, SMS_PAGE_TITLE);
    }
    const dbFields = await FieldModel.find(
      { databaseId: userId },
      { customId: 1, name: 1, type: 1 },
    );
    const masterFields = await fieldMasterModel.find({}, { customId: 1, name: 1, type: 1 });
    const allFields = builtInFields.concat(dbFields, masterFields);
    generatedTemplate.inputs = generatedTemplate.inputs.map((input) => {
      return allFields.find((db) => db.customId === input.customId);
    });

    if (customTheme) {
      const customThemeResult = await setupCustomTheme({
        customTheme,
        databaseId: userId,
        locale: login.locale,
        colors,
      });

      isExistingTheme = customThemeResult.isExistingTheme;

      applyCustomStyleToTemplate(generatedTemplate, customThemeResult.applicableTheme);
      await handleLogoChange(userId, customThemeResult.applicableTheme, generatedTemplate);
    } else if (template.universal && colors && colors.length) {
      generatedTemplate.style.palette.mainColor = colors[0];
      generatedTemplate.style.palette.secondaryColors = colors.slice(1);

      if (generatedTemplate.palette) {
        generatedTemplate.palette = colors;
      }
    }

    const campaignHash = {
      name,
      domainId,
      templateId,
      id: campaignCount,
      templateName: template.name,
      variants: [
        {
          _id: newVariantId,
          name: login.locale === 'hu' ? 'Variáns 1' : 'Variant 1',
          status: 'active',
          lastActivatedDate: new Date(),
          ...(!isFeatureEnabled(account, FEATURES.VARIANT_TEMPLATE_MIGRATED)
            ? { template: generatedTemplate }
            : {}),
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      settings: defaultSettings,
      locale: isTranslatedCampaign ? preferredTemplateLanguage : template.locale,
      isNewCampaign: true,
    };

    if (isEmbedded) {
      campaignHash.version = 3;
    }

    campaign = await CampaignModel.create(campaignHash);
    let variantTemplate;

    if (isFeatureEnabled(account, FEATURES.VARIANT_TEMPLATE_MIGRATED)) {
      variantTemplate = await VariantTemplateModel.create({
        databaseId: userId,
        campaignId: campaign.id,
        variantId: newVariantId,
        template: generatedTemplate,
      });
    }

    log.info('start image copy', { userId, campaignId: campaign._id });
    try {
      await copyImagesAndUpdateCampaign({
        campaign,
        variantTemplate: variantTemplate?.template,
        account,
        userId,
        templateName: template.name,
        log,
      });
    } catch (e) {
      log.error(e);
    }
    log.info('start field copy', { userId, campaignId: campaign._id });
    await copyFieldsAndUpdateCampaign({
      campaign,
      variantTemplate: variantTemplate?.template,
      account,
      userId,
      templateName: template.name,
      log,
    });
    log.info('field copy finished', { userId, campaignId: campaign._id });
    const token = await signToken({ userId }, { expiresIn: 300 });
    omAdapter.uploadHtmlAndPreviewsForVariant({
      model: CampaignModel,
      campaignId: campaignCount,
      variantId: newVariantId,
      token,
      userId,
    });
  } catch (err) {
    log.error('campaign creation error', { err, userId, domainId, campaignId: campaign?._id });
    if (campaign && campaign._id) {
      await CampaignModel.updateOne({ _id: campaign._id }, { status: 'deleted' });
    }

    throw new Error('Failed to create new campaign');
  }

  EventDispatcher.emit(EventDispatcher.events.CAMPAIGN_CREATE, {
    databaseId: userId,
    additionalParams: { templateName: template.name },
  });
  EventDispatcher.emit(EventDispatcher.events.CAMPAIGN_ASSIGN_DOMAIN, {
    databaseId: userId,
  });
  heapAnalytics.trackCampaignCreation(userId, {
    id: campaign.id,
    templateName: template.name,
    templateDisplayName: displayName,
    baseThemeName: getHeapBaseThemeName(template),
    themeName: isExistingTheme && customTheme ? customTheme.name : template.theme,
    themeType: await getThemeType(customTheme, isExistingTheme, template),
    campaignType: getCampaignType(template),
    source: input.source || null,
  });
  updateAccountCampaignInfo(userId);

  if (campaignCreated === false) {
    await AccountModel.updateOne(
      { databaseId: userId },
      { 'settings.onboarding.campaignCreated': true },
    );
  }

  await updateVariantTypes(userId, campaign.id, newVariantId);
  if (isEmbedded) {
    await logCampaignCreateToProfile(userId, 'firstEmbeddedV3Created');
  }

  const defaultIntegration = new DefaultIntegrationHandler(campaign.id, {
    databaseId: userId,
    settings: {
      shops,
      domains,
      integrations,
    },
    features,
  });
  await defaultIntegration.createDefaultIntegration();

  return campaign;
};

const createDynamicContentCampaign = async (input, userId, loginId, log) => {
  const { templateId, domainId, addControlVariant, featureMode = FEATURE_MODE_DC } = input;
  const CampaignModel = model(userId);
  const DynamicContentCampaignModel = dynamicContentCampaignModel(userId);

  let [
    login,
    campaignCount,
    {
      settings: {
        domains,
        onboarding: { campaignCreated },
      },
      features,
    },
  ] = await Promise.all([
    LoginModel.findOne({ _id: loginId }),
    CampaignModel.getMaxCampaignCount(),
    AccountModel.findOne(
      { databaseId: userId },
      {
        'settings.onboarding.campaignCreated': 1,
        'settings.shops': 1,
        'settings.domains': 1,
        features: 1,
      },
    ),
  ]);

  campaignCount += 1;

  const locale = login.locale ?? 'en';
  const prefix = getDCNamePrefix(locale, featureMode);
  const name = `${prefix} #${campaignCount}`;

  const { domain } = domains.find(({ _id }) => _id.toString() === domainId.toString());

  let campaign;
  const newVariantId = ObjectId();
  let controlVariantId;
  try {
    const campaignHash = {
      name,
      domainId,
      domain,
      templateId,
      id: campaignCount,
      variants: [
        {
          _id: newVariantId,
          name: login.locale === 'hu' ? 'Variáns 1' : 'Variant 1',
          status: 'active',
          lastActivatedDate: new Date(),
          changes: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      settings: {},
    };
    const isFrequencyEnabled = features.includes(FEATURES.FREQUENCY_RULE_V2);
    if (isFrequencyEnabled) {
      campaignHash.settings.frequency = {
        showAgain: {
          enabled: false,
          maximumTime: 1,
        },
        delay: {
          enabled: false,
          minimumTime: 1,
          timeUnit: 'second',
        },
        stopEvents: {
          afterClosed: false,
          afterConverted: false,
        },
      };
    }

    if (addControlVariant) {
      controlVariantId = ObjectId();
      campaignHash.variants.push({
        _id: controlVariantId,
        name: login.locale === 'hu' ? 'Kontroll Variáns' : 'Control Variant',
        status: 'active',
        isControlVariant: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    campaign = await DynamicContentCampaignModel.create(campaignHash);
  } catch (err) {
    log.error('campaign creation error', { err, userId, domainId, campaignId: campaign?._id });
    if (campaign && campaign._id) {
      await DynamicContentCampaignModel.updateOne({ _id: campaign._id }, { status: 'deleted' });
    }

    throw new Error('Failed to create new campaign');
  }

  EventDispatcher.emit(EventDispatcher.events.CAMPAIGN_ASSIGN_DOMAIN, {
    databaseId: userId,
  });

  EventDispatcher.emit(EventDispatcher.events.CAMPAIGN_CREATE, {
    databaseId: userId,
    additionalParams: { type: 'dynamic_content' },
  });

  heapAnalytics.trackCampaignCreation(userId, {
    id: campaign.id,
    themeType: 'other',
    themeName: 'Dynamic content',
    type: 'dynamic_content',
  });

  updateAccountCampaignInfo(userId);

  if (campaignCreated === false) {
    await AccountModel.updateOne(
      { databaseId: userId },
      { 'settings.onboarding.campaignCreated': true },
    );
  }
  await Promise.all([
    queueVariantTypeUpdate(userId, campaign.id, newVariantId),
    ...(addControlVariant ? [queueVariantTypeUpdate(userId, campaign.id, controlVariantId)] : []),
  ]);
  await logCampaignCreateToProfile(userId, 'firstDynamicCampaignCreated');

  return campaign;
};

const createCampaign = async (_, { input }, { userId, loginId, log }) => {
  const { dynamicContent } = input;

  if (dynamicContent) {
    const dynamicContentCampaign = await createDynamicContentCampaign(input, userId, loginId, log);
    return dynamicContentCampaign;
  }

  const campaign = await createNormalCampaign(input, userId, loginId, log);
  return campaign;
};

const getCampaign = async (_, { campaignId }, { userId }) => {
  const campaign = await model(userId).findOne({ id: campaignId });

  if (campaign.domainId) {
    const setting = await AccountModel.findOne(
      { databaseId: userId, 'settings.domains._id': campaign.domainId },
      { 'settings.domains.$': 1 },
    );
    if (setting) {
      campaign.domain = setting.settings.domains[0].domain;
    }
  }

  return campaign;
};

const getCampaignSegments = async (campaign) => {
  const segments = { shopifySegment: null, nonShopifySegment: null };

  if (campaign.templateId) {
    const template = await templateModel.findOne({ _id: campaign.templateId }, { _id: 1 });
    if (template) {
      const useCase = await UseCaseModel.findOne(
        { templates: template._id },
        { shopifySegment: 1, nonShopifySegment: 1 },
      );
      if (useCase) {
        segments.shopifySegment = useCase.shopifySegment;
        segments.nonShopifySegment = useCase.nonShopifySegment;
      }
    }
  }

  return segments;
};

const getCampaignSettings = async (_, { campaignId }, ctx) => {
  const { userId, superadmin } = ctx;
  let [account, campaign, campaignVariants, hasViewedCategoryProductFilter] = await Promise.all([
    AccountModel.findOne({ databaseId: userId }, { features: 1 }),
    model(userId).findOne({ id: campaignId }).lean(),
    model(userId).findOne(
      { id: campaignId },
      {
        'variants._id': 1,
        'variants.template.inputs.type': 1,
        'variants.template.elements': 1,
        'variants.status': 1,
        'variants.template.style.mode': 1,
      },
    ),
    model(userId).findOne(
      {
        id: campaignId,
        'variants.template.elements': {
          $elemMatch: {
            type: 'OmProduct',
            'data.mode': 'most-viewed',
            'data.productFilter.type': 'viewed-category',
          },
        },
      },
      { _id: 1 },
    ),
  ]);

  if (!campaign) {
    return {};
  }

  await addAlertsToCampaigns(userId, [campaign], []);

  const { variants = [] } = campaignVariants || {};

  const experiences = await ExperienceModel.find({
    campaign: campaign._id,
    deletedAt: { $exists: false },
  }).sort({ priority: -1 });

  campaign.experiences = experiences;

  const resolvedExperimentName = (await resolveExperimentName([campaign]))[0].currentExperimentName;
  campaign.currentExperimentName = resolvedExperimentName;

  campaign.segments = await getCampaignSegments(campaign);

  const availableVariants = variants.filter((variant) => variant.status !== 'deleted');

  if (isFeatureEnabled(account, FEATURES.VARIANT_TEMPLATE_MIGRATED)) {
    const availableVariantIds = availableVariants.map((variant) => variant._id);
    const variantTemplates = await VariantTemplateModel.find(
      {
        databaseId: userId,
        campaignId: campaign.id,
        variantId: { $in: availableVariantIds },
      },
      { variantId: 1, 'template.inputs': 1, 'template.elements': 1, 'template.style.mode': 1 },
    );

    availableVariants.forEach((variant) => {
      const variantTemplate = variantTemplates.find(
        (template) => `${template.variantId}` === `${variant._id}`,
      );
      variant.template = variantTemplate?.template;
    });

    const hasViewedCategoryProductFilterInVariant = variantTemplates.find((template) => {
      return template.template?.elements?.find(
        (element) =>
          element.type === 'OmProduct' &&
          element.data?.mode === 'most-viewed' &&
          element.data?.productFilter?.type === 'viewed-category',
      );
    });
    if (hasViewedCategoryProductFilterInVariant) {
      hasViewedCategoryProductFilter = true;
    }
  }

  if (campaign.version === 1) {
    campaign.hasEmailField = true;
    campaign.hasPhoneField = false;
    campaign.noInputField = false;
    campaign.hasCoupon = false;
  } else {
    campaign.hasEmailField =
      availableVariants.length &&
      availableVariants.some(
        (v) => v && v.template && v?.template?.inputs.some((i) => i?.type === 'email'),
      );
    campaign.hasPhoneField =
      availableVariants.length &&
      availableVariants.some(
        (v) => v && v.template && v?.template?.inputs.some((i) => i?.type === 'phoneNumber'),
      );
    campaign.hasCoupon =
      availableVariants.length &&
      availableVariants.some(
        (v) =>
          v &&
          v.template?.elements?.some(
            (element) => element.type === 'OmCoupon' && element?.data?.coupon?.type !== 'followup',
          ),
      );
    campaign.noInputField =
      !availableVariants.length ||
      availableVariants.every((v) => !v.template || v.template?.inputs?.length === 0);
    campaign.hasProductElement =
      availableVariants.length &&
      availableVariants.some((v) =>
        v?.template?.elements?.some((element) => element?.type === 'OmProduct'),
      );
  }

  if (campaign.domainId) {
    const setting = await AccountModel.findOne(
      { databaseId: userId, 'settings.domains._id': campaign.domainId },
      { 'settings.domains.$': 1 },
    );
    if (setting) {
      campaign.domain = setting.settings.domains[0].domain;
    }
  }

  if (campaign.templateId) {
    const template = await templateModel.findOne(
      { _id: campaign.templateId },
      { previewUrls: 1, type: 1 },
    );
    if (template) {
      if (template.previewUrls[0]) campaign.previewUrl = template.previewUrls[0];
      campaign.templateType = template.type;
    } else {
      campaign.templateType = _get(availableVariants, '0.template.style.mode', 'Unknown');
    }
  } else if (campaign.type) {
    campaign.templateType = campaign.type;
  }

  const {
    settings: { integrations: globalIntegrations },
    features,
  } = await AccountModel.findOne(
    { databaseId: userId },
    { 'settings.integrations': 1, features: 1 },
  );

  if (campaign.settings.integrations) {
    for (let integration of campaign.settings.integrations) {
      const globalIntegration = globalIntegrations.find((i) =>
        ObjectId(i._id).equals(ObjectId(integration.id)),
      );
      integration.global = globalIntegration;
    }
  }

  campaign.hasViewedCategoryProductFilter = !!hasViewedCategoryProductFilter;
  campaign.priority = campaign.settings.priority ?? 'NORMAL';
  campaign.manuallyEmbedded = campaign.settings.manuallyEmbedded;
  campaign.events = campaign.settings.events;
  campaign.positions = campaign.settings.positions;
  campaign.frequency = campaign.settings.frequency;
  campaign.experiences = experiences;

  campaign.frontendRules = checkFrontEndRules(campaign.settings.frontendRules, {
    superadmin,
    features,
  });

  const isFrequencyEnabled = features.includes(FEATURES.FREQUENCY_RULE_V2);
  if (isFrequencyEnabled) {
    campaign.frontendRules = campaign.frontendRules.filter((e) => e.type !== 'maximumPopupDisplay');
  } else {
    campaign.frequency = null;
  }

  campaign.integrations = campaign.settings.integrations;
  campaign.analyticsType = campaign.settings.analyticsType;
  campaign.stopTestRunning = campaign.settings.stopTestRunning;
  campaign.notifyMe = campaign.settings.notifyMe || {
    status: false,
    emails: [],
  };
  campaign.recommendedIntegrations = campaign.settings.recommendedIntegrations;
  if (typeof campaign.settings.lastSaved === 'undefined') {
    campaign.lastSaved = '';
  } else {
    campaign.lastSaved = campaign.settings.lastSaved;
  }

  delete campaign.settings;

  return campaign;
};

const getRecommendedIntegrations = async (_, { campaignId }, { userId }) => {
  const campaign = await model(userId).findOne({ id: campaignId });
  if (!campaign) {
    return {};
  }

  return campaign.settings?.recommendedIntegrations;
};

const getRecommendedIntegrationsState = async (_, { integrationType, campaignId }, { userId }) => {
  let isKlaviyoDetected = false;
  const { domainId } = await model(userId).findOne({ id: campaignId });
  const {
    settings: { integrations, domains },
  } = (await AccountModel.findOne(
    { databaseId: userId, 'settings.domains._id': ObjectId(domainId) },
    { 'settings.integrations': 1, 'settings.domains.$': 1 },
  )) || { settings: {} };
  const sameIntegration = integrations?.filter((i) => i.type === integrationType) ?? [];

  const globalIntegrationIds = sameIntegration.map((i) => i.id);
  const campaigns = await model(userId).find({
    'settings.integrations.id': { $in: globalIntegrationIds },
    status: { $ne: 'deleted' },
  });

  if (integrationType === 'klaviyo' && domains && domains.length) {
    isKlaviyoDetected = domains[0]?.isKlaviyoDetected;
  }

  const recommendationStateResolver = new RecommendationStateResolver(
    integrationType,
    sameIntegration.length,
    campaigns.length,
    isKlaviyoDetected,
  );
  return recommendationStateResolver.getRecommendationState();
};

const rejectRecommendedIntegration = async (_, { campaignId, integrationType }, { userId }) => {
  const campaign = await model(userId).findOne({ id: campaignId });
  if (!campaign) {
    return false;
  }

  const recommendedIntegrations = campaign.settings?.recommendedIntegrations;
  recommendedIntegrations[integrationType] = false;

  await model(userId).updateOne(
    { id: campaignId },
    { $set: { 'settings.recommendedIntegrations': recommendedIntegrations } },
  );
  return true;
};

const _mapOldCampaignProgressState = (options) => {
  if (options.expression) {
    let optionKeys = Object.keys(options);
    optionKeys.forEach((optionKey) => {
      options[optionKey].forEach((option) => {
        if (option.campaignId === 0 || option.campaignId === '0') option.campaignId = '';
      });
    });
    options.expressions = [options.expression];
    delete options.expression;
  }
  return options;
};

const checkFrontEndRules = (frontendRules, { superadmin, features }) => {
  let ruleKeys = Object.keys(frontendRules);
  ruleKeys.forEach((key) => {
    let rule = frontendRules[key];
    if (rule.type === 'campaignProgressState') {
      rule.options = _mapOldCampaignProgressState(rule.options);
    }
  });

  return frontendRules;
};

const copyExperiences = async ({ campaign, variants }, databaseId) => {
  const variantsMap = variants.reduce((map, variantPair) => {
    map[variantPair.old.toString()] = variantPair.new;
    return map;
  }, {});

  const experiences = ExperienceModel.find({
    campaign: campaign.old,
    databaseId,
    deletedAt: { $exists: false },
  });

  for await (const experience of experiences) {
    experience._id = ObjectId();
    experience.isNew = true;
    experience.campaign = campaign.new;
    experience.variants = experience.variants
      .map((variantId) => variantsMap[variantId?.toString()])
      .filter((variantId) => variantId);
    await experience.save();
  }
};

const getNewVariantTemplate = async ({ campaignId, variantId, databaseId }) => {
  const variantTemplate = await getVariantTemplate({ campaignId, variantId, databaseId });
  if (variantTemplate) {
    delete variantTemplate.template['style.data.tabText'];
    return regenerateTemplateIds(variantTemplate.template);
  }

  return null;
};

const copyNormalCampaign = async (campaign, userId, log) => {
  const CampaignModel = model(userId);

  const account = await AccountModel.findOne(
    { databaseId: userId },
    {
      features: 1,
    },
  );

  if (campaign && campaign.version >= 2) {
    const oldCampaignId = campaign.id;
    const campaignCount = await getIncreasedCampaignCount(userId);
    const newCampaignId = ObjectId();
    const references = {
      campaign: { old: campaign._id, new: newCampaignId },
      variants: [],
    };
    campaign._id = newCampaignId;
    campaign.id = campaignCount;
    campaign.name =
      campaign.locale === 'hu' ? `${campaign.name} másolata` : `Copy of ${campaign.name}`;
    campaign.isNew = true;
    campaign.impressions = 0;
    campaign.conversions = 0;
    campaign.conversionRate = 0;
    campaign.status = 'inactive';
    campaign.currentExperimentId = null;
    campaign.settings.positions = null;
    campaign.settings.manuallyEmbedded = false;
    campaign.variants = campaign.variants.filter((v) => v.status !== 'deleted');

    const newVariantIds = [];
    const variantTemplates = [];
    for (let v of campaign.variants) {
      const oldVariantId = v._id;
      let newVariantId = ObjectId();
      references.variants.push({ old: v._id, new: newVariantId });
      v._id = newVariantId;
      v.id = undefined;

      if (isFeatureEnabled(account, FEATURES.VARIANT_TEMPLATE_MIGRATED)) {
        const variantTemplate = await getNewVariantTemplate({
          campaignId: oldCampaignId,
          variantId: oldVariantId,
          databaseId: userId,
        });
        if (variantTemplate) {
          variantTemplates.push({
            databaseId: userId,
            campaignId: campaign.id,
            variantId: newVariantId,
            template: variantTemplate,
          });
        }
      } else if (v.template) {
        delete v.template['style.data.tabText'];
        v.template = regenerateTemplateIds(v.template);
      }

      v.impressions = 0;
      v.conversions = 0;
      v.confidence = null;
      v.winner = false;
      newVariantIds.push(newVariantId);
      v.previewGenerated = false;
      v.createdAt = new Date();
      v.updatedAt = new Date();
    }
    campaign.createdAt = new Date();
    campaign.updatedAt = new Date();
    await Promise.all([
      campaign.save(),
      copyExperiences(references, userId),
      ...(isFeatureEnabled(account, FEATURES.VARIANT_TEMPLATE_MIGRATED) && variantTemplates?.length
        ? [VariantTemplateModel.insertMany(variantTemplates)]
        : []),
    ]);

    updateAccountCampaignInfo(userId);

    const token = await signToken({ userId }, { expiresIn: 300 });
    log.info('copyCampaign, html generation started for variants', {
      newCampaignId,
      notDeletedVariants: campaign.variants.map((v) => v._id),
    });
    for (let v of campaign.variants) {
      omAdapter.uploadHtmlAndPreviewsForVariant({
        model: CampaignModel,
        campaignId: campaignCount,
        variantId: v._id,
        token,
        userId,
      });
    }
    log.info('copyCampaign, html generation finished');
    EventDispatcher.emit(EventDispatcher.events.CAMPAIGN_COPY, {
      databaseId: userId,
    });

    if (isFeatureEnabled(account, FEATURES.VARIANT_TEMPLATE_MIGRATED)) {
      campaign.variants.forEach((v) => {
        v.template = variantTemplates.find(
          (template) => `${template.variantId}` === `${v._id}`,
        )?.template;
      });
    }
    await copyCouponConfigConfigsForCampaign({ variants: campaign.variants, userId });

    await checkMissingBindings(userId, campaign);

    return { success: true, campaignId: campaign.id };
  }
};

const copyDynamicContentCampaign = async (campaignId, userId, log) => {
  const DynamicContentCampaignModel = dynamicContentCampaignModel(userId);
  const campaign = await DynamicContentCampaignModel.findOne({ _id: campaignId });
  const newCampaignId = ObjectId();
  const campaignCount = await getIncreasedCampaignCount(userId);

  const references = {
    campaign: { old: campaign._id, new: newCampaignId },
    variants: [],
  };
  campaign._id = newCampaignId;
  campaign.id = campaignCount;
  campaign.name =
    campaign.locale === 'hu' ? `${campaign.name} másolata` : `Copy of ${campaign.name}`;
  campaign.isNew = true;
  campaign.impressions = 0;
  campaign.conversions = 0;
  campaign.conversionRate = 0;
  campaign.status = 'inactive';
  campaign.currentExperimentId = null;
  const newVariantIds = [];
  const notDeletedVariants = campaign.variants.filter((v) => v.status !== 'deleted');

  for (const v of notDeletedVariants) {
    const oldVariantId = v._id;
    let newVariantId = ObjectId();
    references.variants.push({ old: oldVariantId, new: newVariantId });
    v._id = newVariantId;
    v.id = undefined;
    v.impressions = 0;
    v.conversions = 0;
    v.confidence = null;
    v.winner = false;
    newVariantIds.push(newVariantId);
    v.createdAt = new Date();
    v.updatedAt = new Date();

    const changeId = ObjectId(v.changes);
    const dynamicContent = await dynamicContentModel.findOne({ _id: changeId });
    const { _id: newDynamicContentId } = await dynamicContentModel.create({
      databaseId: userId,
      campaignId: campaignCount,
      variantId: newVariantId,
      changes: dynamicContent?.changes,
    });

    v.changes = newDynamicContentId;

    await copyEvaluations({
      databaseId: userId,
      newCampaignId: campaignCount,
      oldVariantId,
      newVariantId,
    });
  }
  campaign.createdAt = new Date();
  campaign.updatedAt = new Date();
  await Promise.all([campaign.save(), copyExperiences(references, userId)]);

  updateAccountCampaignInfo(userId);

  EventDispatcher.emit(EventDispatcher.events.CAMPAIGN_COPY, {
    databaseId: userId,
  });

  return { success: true, campaignId: campaign.id };
};

const copyCampaign = async (_, { campaignId, templateType }, { userId, log }) => {
  if (templateType === 'dynamic_content') {
    const res = await copyDynamicContentCampaign(campaignId, userId, log);

    if (res.success && res.campaignId) {
      heapAnalytics.trackCampaignCreation(userId, {
        id: res.campaignId,
        templateType,
        themeType: 'other',
        themeName: 'Dynamic content',
        type: 'dynamic_content',
        source: 'copy',
      });
    }

    return res;
  }

  const CampaignModel = model(userId);
  const campaign = await CampaignModel.findOne({ _id: campaignId }).select('+variants.template');
  const res = await copyNormalCampaign(campaign, userId, log);

  if (res.success && res.campaignId) {
    heapAnalytics.trackCampaignCreation(userId, {
      id: res.campaignId,
      templateName: campaign.name,
      themeName: campaign.theme || 'unknown',
      themeType: 'copy',
      campaignType: campaign.type || 'standard',
      source: 'copy',
    });
  }

  return res;
};

const deleteCampaigns = async (_, { campaignIds, permanent = false }, { userId, log }) => {
  log.info('[DELETE CAMPAIGNS]', { campaignIds, permanent });
  const account = await AccountModel.findOne({ databaseId: userId }, { features: 1 });
  const CampaignModel = model(userId);
  let success = true;
  const campaigns = [];

  for (let campaignId of campaignIds) {
    let resp;

    await removeCampaignFromExperiment(campaignId, userId);

    if (permanent) {
      let hasCustomThemeIds;

      if (isFeatureEnabled(account, FEATURES.VARIANT_TEMPLATE_MIGRATED)) {
        resp = await CampaignModel.findOneAndDelete({ _id: campaignId }).select('id');
        const deletedTemplates = await VariantTemplateModel.find({
          campaignId: resp.id,
          databaseId: userId,
        });
        await VariantTemplateModel.deleteMany({
          campaignId: resp.id,
          databaseId: userId,
        });
        hasCustomThemeIds = deletedTemplates
          .map((variantTemplate) => variantTemplate.template?.themeKit?.id)
          .filter((id) => id);
      } else {
        resp = await CampaignModel.findOneAndDelete({ _id: campaignId }).select(
          'variants.template.themeKit.id',
        );
        hasCustomThemeIds = resp.variants
          .map((variant) => variant.template?.themeKit?.id)
          .filter((id) => id);
      }

      if (hasCustomThemeIds.length) {
        await removeCampaignCustomTemplate(account, userId, hasCustomThemeIds);
      }
    } else {
      resp = await CampaignModel.findOneAndUpdate(
        { _id: campaignId },
        { status: 'deleted', $unset: { currentExperimentId: 1 } },
      );
    }

    await smartHeadlineHelper.removeSmartHeadlines({ campaignId: resp.id, databaseId: userId });

    campaigns.push(resp);
    success = success && !!resp;
  }

  updateAccountCampaignInfo(userId);
  updateUserCaches(userId);

  EventDispatcher.emit(EventDispatcher.events.CAMPAIGN_DELETE, {
    databaseId: userId,
  });

  return true;
};

const removeCampaignCustomTemplate = async (account, userId, customThemeIds) => {
  const oneDayAgo = moment().subtract(1, 'days');
  for (const customThemeId of customThemeIds) {
    let numberOfCampaignsWithTheTheme = 0;
    if (isFeatureEnabled(account, FEATURES.VARIANT_TEMPLATE_MIGRATED)) {
      numberOfCampaignsWithTheTheme = await VariantTemplateModel.distinct('campaignId', {
        'template.themeKit.id': customThemeId,
      });
    } else {
      numberOfCampaignsWithTheTheme = await model(userId).countDocuments({
        'variants.template.themeKit.id': customThemeId,
      });
    }

    // If no campaign uses it, we can set the deletedAt flag
    if (numberOfCampaignsWithTheTheme === 0) {
      await CustomThemeModel.findOneAndUpdate(
        {
          _id: customThemeId,
          databaseId: userId,
          sourceTheme: { $exists: true },
          $and: [{ createdAt: { $exists: true } }, { createdAt: { $gt: oneDayAgo.toDate() } }],
        },
        {
          deletedAt: new Date(),
        },
      );
    }
  }
};

const saveCampaignSettings = async (_, { input }, userContext) => {
  const {
    userId: databaseId,
    superadmin,
    superAdminName,
    superAdminEmail,
    role,
    loginId,
  } = userContext;
  const CampaignModel = model(databaseId);
  const {
    _id,
    name,
    domain,
    domainId,
    device,
    status,
    schedule,
    events,
    frontendRules,
    frequency,
    recommendedIntegrations,
    integrations,
    analyticsType,
    stopTestRunning,
    notifyMe,
    positions,
    manuallyEmbedded,
    priority,
    experiences,
  } = input;

  if (Array.isArray(experiences) && experiences.length) {
    await updateCampaignExperiencesRules({
      campaignId: _id,
      frontendRules,
      databaseId,
    });
  }

  let update = {
    name,
    device,
    domain,
    status,
    schedule,
    settings: {
      frequency,
      stopTestRunning,
      events,
      frontendRules,
      recommendedIntegrations,
      integrations,
      analyticsType,
      notifyMe,
      positions,
      manuallyEmbedded,
      priority,
      lastSaved: new Date(),
    },
  };

  if (domainId) {
    update.domainId = domainId;
  }

  const campaign = await CampaignModel.findOneAndUpdate({ _id }, update, { new: false });

  createChangelogs(campaign, update, {
    userId: databaseId,
    superadmin,
    superAdminName,
    superAdminEmail,
    role,
    loginId,
  });

  updateUserCaches(databaseId);
  heapAnalytics.trackCampaignSettingsUpdate(databaseId, { id: campaign.id });
  EventDispatcher.emit(EventDispatcher.events.CAMPAIGN_UPDATE, { databaseId });
  jetfabric.reportFeatureActivation(databaseId);
  return campaign;
};

const K_FRONTEND_RULES = 'frontendRules';
const _settingsKey = [K_FRONTEND_RULES, 'priority'];

const changeCampaignSettings = async (_, { input }, userContext) => {
  const { userId, superadmin, superAdminName, superAdminEmail, role, loginId } = userContext;
  const CampaignModel = model(userId);
  const _id = await idToMongoId(userId, input._id);
  let update = {};

  const isBlank = (v) => _isEmpty(v) && typeof v !== 'number';

  for (let key in input) {
    if (
      key !== '_id' &&
      input.hasOwnProperty(key) &&
      (_settingsKey.includes(key) || !isBlank(input[key]))
    ) {
      if (key === K_FRONTEND_RULES) {
        await updateCampaignExperiencesRules({
          campaignId: _id,
          frontendRules: input[key],
          databaseId: userId,
        });
      }
      if (_settingsKey.includes(key)) {
        update[`settings.${key}`] = input[key];
      } else {
        update[key] = input[key];
      }

      if (key === 'domainId') {
        const campaign = await CampaignModel.findOne({ id: input._id }, { status: 1, domainId: 1 });
        const domainId = input[key];

        if (
          (!campaign.domainId || !campaign.domainId.equals(domainId)) &&
          campaign.status === 'active'
        ) {
          const account = await AccountModel.findOne(
            { databaseId: userId },
            { 'settings.domains': 1, 'limits.domains': 1 },
          );
          const newDomain = account.settings.domains.find((domain) => domain._id.equals(domainId));

          if (newDomain.inactive) {
            const activeDomains = account.settings.domains.filter((domain) => !domain.inactive);
            if (activeDomains.length >= account.limits.domains) {
              return {
                success: false,
                message: 'campaignDomainChangeDomainLimitReached',
              };
            }
          }
        }
      }

      if (key === 'domain') {
        EventDispatcher.emit(EventDispatcher.events.CAMPAIGN_ASSIGN_DOMAIN, {
          databaseId: userId,
        });
      }
    }
  }

  const campaign = await CampaignModel.findOneAndUpdate({ _id }, update, { new: false });
  createChangelogs(campaign, update, {
    userId,
    superadmin,
    superAdminName,
    superAdminEmail,
    role,
    loginId,
  });
  updateUserCaches(userId);

  return { success: true, message: null };
};

const updateTestRunningSetting = async (_, { input }, { userId }) => {
  const CampaignModel = model(userId);
  const _id = await idToMongoId(userId, input._id);

  const res = await CampaignModel.findOneAndUpdate(
    { _id },
    { 'settings.stopTestRunning': input.stopTestRunning },
  );
  return !!res;
};

const savePreview = async (_, { campaignId, variantId, template }, { userId }) => {
  const CACHE_TIME = 86400; // 24 hours
  const CACHE_KEY = redis.getPreviewCacheKey(userId, campaignId, variantId);

  await redis.deletePreviewCache(userId, campaignId, variantId);

  await redis.setex(CACHE_KEY, JSON.stringify(template), CACHE_TIME);
  return true;
};

const deleteVariant = async (_, { variantId }, userContext) => {
  const { userId, superadmin, superAdminName, superAdminEmail, role, loginId, log } = userContext;
  log.info('delete variant', { variantId });
  const account = await AccountModel.findOne({ databaseId: userId }, { features: 1 });
  const CampaignModel = model(userId);
  const updated = await CampaignModel.findOneAndUpdate(
    {
      'variants._id': ObjectId(variantId),
    },
    { $set: { 'variants.$.status': 'deleted' } },
    {
      new: true,
      projection: {
        'variants.template': 1,
        'variants.status': 1,
        'variants._id': 1,
        type: 1,
        id: 1,
        _id: 1,
      },
    },
  );

  queueCampaignChange({
    changeContext: {
      campaignId: updated._id,
      variantId: ObjectId(variantId),
    },
    changeType: campaignChangeTypes.VARIANT_DELETED,
    userContext: { userId, superadmin, superAdminName, superAdminEmail, role, loginId },
  });

  const activeOrInactiveVariants = updated.variants.filter((v) => v.status !== 'deleted');

  if (activeOrInactiveVariants && activeOrInactiveVariants.length === 1) {
    await CampaignModel.update(
      { 'variants._id': ObjectId(activeOrInactiveVariants[0]._id) },
      { 'variants.$.status': 'active' },
    );
  }
  updateUserCaches(userId);

  EventDispatcher.emit(EventDispatcher.events.CREATIVE_DELETE, {
    databaseId: userId,
  });

  // delete unused bindings from integrations
  const variant = updated.variants.find((v) => v._id.toString() === variantId);
  if (variant.isControlVariant || updated.type === 'dynamic_content') return true;

  let inputs;
  let variantTemplates = [];

  if (isFeatureEnabled(account, FEATURES.VARIANT_TEMPLATE_MIGRATED)) {
    variantTemplates = await VariantTemplateModel.find({
      databaseId: userId,
      campaignId: updated.id,
      variantId: {
        $in: [variantId, ...activeOrInactiveVariants.map((v) => v._id)],
      },
    });

    if (variantTemplates.length > 0) {
      const deletedVariantTemplate = variantTemplates.find(
        (v) => v.variantId.toString() === variantId,
      );
      inputs = deletedVariantTemplate.template.elements.filter(
        (element) => element.type === 'OmInput',
      );
    }
  } else {
    inputs = variant.template.elements.filter((element) => element.type === 'OmInput');
  }

  const idsToDelete = [];
  for (const input of inputs) {
    const { customId } = input.data.form.customSettings;
    if (
      !updated.variants.find((item) => {
        const remainingVariantTemplate = !isFeatureEnabled(
          account,
          FEATURES.VARIANT_TEMPLATE_MIGRATED,
        )
          ? item.template
          : variantTemplates.find((v) => v.variantId.toString() === item._id.toString())?.template;
        return (
          item.status !== 'deleted' &&
          remainingVariantTemplate?.elements.find(
            (element) =>
              element.type === 'OmInput' && element.data.form.customSettings.customId === customId,
          )
        );
      })
    ) {
      idsToDelete.push(customId);
    }
  }

  await CampaignModel.updateOne(
    { _id: updated._id },
    {
      $pull: {
        'settings.integrations.$[].bindings': { fieldId: { $in: idsToDelete } },
      },
    },
    { multi: true },
  );

  await removeCouponConfigsForVariant({ variantId, userId });
  await smartHeadlineHelper.removeSmartHeadlines({ variantId });

  return true;
};

const campaignList = async (_, { filter }, { userId }, { fieldNodes }) => {
  let requestedFields = getRequestedFields({ selectionSet: fieldNodes[0].selectionSet });
  requestedFields = requestedFields.reduce((mongoProjection, field) => {
    mongoProjection[field] = 1;
    return mongoProjection;
  }, {});
  let domains;
  if (filter?.domainIds?.length) {
    domains = { $in: filter.domainIds };
  }

  let campaigns = await model(userId)
    .find({ ...(domains && { domainId: domains }) }, { ...requestedFields })
    .sort({ name: 1 })
    .lean();

  if (requestedFields.currentExperimentId) {
    campaigns = await resolveExperimentName(campaigns);
  }

  return campaigns;
};

const idToMongoId = async (userId, id) => {
  const isObjectIdLength = id.length === 24;
  return isObjectIdLength ? ObjectId(id) : (await model(userId).findOne({ id }, { _id: 1 }))._id;
};

const allVariants = async (_, { campaignId, interval }, { userId }) => {
  campaignId = await idToMongoId(userId, campaignId);

  const variantsAggregation = allVariantsWithPeriodBasedStats({
    userId,
    campaignId,
    interval,
  });

  variantsAggregation.options = { collation: { locale: 'en_US', strength: 1 } };

  const [data, [{ count }]] = await Promise.all([
    variantsAggregation,
    model(userId).aggregate([
      { $match: { _id: campaignId } },
      {
        $project: {
          variants: {
            $filter: {
              input: '$variants',
              as: 'v',
              cond: { $ne: ['$$v.status', 'deleted'] },
            },
          },
        },
      },
      { $unwind: '$variants' },
      { $count: 'count' },
    ]),
  ]);
  let variants = [];

  for (let row of data) {
    row.variants.isPrettyBackground = isPrettyBackground(row.variants.overlayBg);
    variants.push(row.variants);
  }

  return { variants, count };
};

const changeVariantStatus = async (_, { variantId, status }, userContext) => {
  const { userId, superadmin, superAdminName, superAdminEmail, role, loginId } = userContext;
  let setObject = { 'variants.$.status': status };
  const [{ variants, _id }, experiences] = await Promise.all([
    model(userId)
      .findOne(
        { 'variants._id': variantId },
        { id: 1, 'variants.status': 1, 'variants._id': 1, 'variants.isControlVariant': 1 },
      )
      .lean(),
    ExperienceModel.find(
      {
        variants: ObjectId(variantId),
        databaseId: userId,
        deletedAt: { $exists: false },
      },
      { campaign: 1, variants: 1 },
    ),
  ]);

  const mappedVariants = variants.map((variant) => {
    if (variant._id.toString() === variantId.toString()) variant.status = 'active';
    return variant;
  });

  const currentVariant = mappedVariants.find((v) => v._id.toString() === variantId.toString());

  if (status === 'active') {
    const lonelyControlVariant = getLonelyControlVariant(experiences, mappedVariants, 1);
    setObject['variants.$.lastActivatedDate'] = new Date();

    if (lonelyControlVariant?.toString() === variantId.toString())
      return { success: false, message: 'lonely_variant' };
  } else if (status === 'inactive') {
    const lonelyControlVariant = getLonelyControlVariant(experiences, mappedVariants, 2);

    if (lonelyControlVariant && !currentVariant?.isControlVariant) {
      await model(userId).updateOne({ 'variants._id': lonelyControlVariant }, { $set: setObject });
    }
  }

  queueCampaignChange({
    changeContext: {
      campaignId: _id,
      variantId,
    },
    changeType:
      status === 'active'
        ? campaignChangeTypes.VARIANT_ACTIVATED
        : campaignChangeTypes.VARIANT_INACTIVATED,
    userContext: { userId, superadmin, superAdminName, superAdminEmail, role, loginId },
  });

  await model(userId).updateOne({ 'variants._id': variantId }, { $set: setObject });
  updateUserCaches(userId);

  return { success: true, message: '' };
};

const isInExperiment = (campaign, userId, log) => {
  if (campaign.currentExperimentId) {
    log.warn("campaign status can't be changed if in experiment: %o", {
      campaignId: campaign._id.toString(),
      userId,
      currentExperimentId: campaign.currentExperimentId,
    });
    return true;
  }
  return false;
};

const getLonelyControlVariant = (experiences, variants, allowed) => {
  let activeVariants = variants.filter((variant) => variant.status === 'active').length;
  let controlVariantId = null;
  let isLonely;

  const checkVariant = (variant) => {
    if (variant.isControlVariant && variant.status === 'active') {
      controlVariantId = variant._id;
    }

    return activeVariants <= allowed && controlVariantId;
  };

  if (experiences.length) {
    isLonely = experiences.some((experience) => {
      controlVariantId = null;
      const variantIds = experience.variants.map((variant) => variant._id.toString());
      activeVariants = variants.filter(
        (variant) => variant.status === 'active' && variantIds.includes(variant._id.toString()),
      ).length;

      return variants.some((variant) => {
        return variantIds.includes(variant._id.toString()) && checkVariant(variant);
      });
    });
  } else {
    isLonely = variants.some((variant) => {
      return checkVariant(variant);
    });
  }

  return isLonely ? controlVariantId : null;
};

const changeCampaignStatus = async (_, { campaignId, status }, userContext) => {
  const { userId, superadmin, superAdminName, superAdminEmail, role, log, loginId } = userContext;
  let inExperiment = null;
  campaignId = await idToMongoId(userId, campaignId);
  const campaign = await model(userId).findOne({ _id: campaignId });
  const template = await templateModel.findOne({ _id: campaign.templateId });

  if (status === 'active') {
    // set campaign activation in profile object
    await setUserProfileKey({
      databaseId: userId,
      key: 'onboardingSectionActivated',
      value: true,
      log,
    });
    let campaignLimit;

    const account = await AccountModel.findOne(
      { databaseId: userId },
      {
        type: 1,
        'limits.campaigns': 1,
        'settings.onboarding': 1,
        'limits.domains': 1,
        'settings.domains': 1,
        'billing.package': 1,
        features: 1,
      },
    );
    campaignLimit = account.limits.campaigns;
    let domainLimit = account.limits.domains;
    if (account.type === 'sub') {
      const master = await AccountModel.findOne(
        { subAccounts: { $in: [account._id] } },
        { 'limits.campaigns': 1 },
      );
      campaignLimit = master.limits.campaigns;
      domainLimit = 9999;
    }

    const [activeCampaigns, campaignVariants] = await Promise.all([
      model(userId).count({ status: 'active' }),
      model(userId).findOne(
        { _id: campaignId, 'variants.status': { $in: ['active', 'inactive'] } },
        { 'variants.template': 0 },
      ),
    ]);
    inExperiment = isInExperiment(campaignVariants, userId, log);
    if (inExperiment) {
      return { success: false, message: '' };
    }

    if (activeCampaigns + 1 > campaignLimit) {
      return { message: 'activeCampaignLimitReached', success: false };
    }

    const domain = account.settings.domains.find((domain) =>
      domain._id.equals(campaignVariants.domainId),
    );
    if (domain.inactive) {
      const activeDomains = account.settings.domains.filter((domain) => !domain.inactive);
      if (activeDomains.length >= domainLimit) {
        return { success: false, message: 'upgrade', triggers: ['sites'] };
      }

      await AccountModel.updateOne(
        { databaseId: userId, 'settings.domains._id': domain._id },
        { $set: { 'settings.domains.$.inactive': false } },
      );
    }

    campaignVariants.variants = campaignVariants.variants.filter((variant) =>
      ['active', 'inactive'].includes(variant.status),
    );

    if (campaignVariants && campaignVariants.variants.length === 1) {
      await model(userId).update(
        { 'variants._id': campaignVariants.variants[0]._id },
        { 'variants.$.status': 'active' },
      );
    }

    if (campaign.type === 'dynamic_content') {
      await setRunningSABTests(userId, campaign);
    }
  } else {
    const campaign = await model(userId).findOne({ _id: campaignId }, { currentExperimentId: 1 });
    inExperiment = isInExperiment(campaign, userId, log);
    if (inExperiment) {
      return { success: false, message: '' };
    }
  }

  heapAnalytics.trackCampaignStatusChange(userId, {
    id: campaignId,
    status,
    templateType: template?.type ? template?.type : DYNAMIC_CONTENT_TYPE,
  });

  await model(userId).updateOne({ _id: campaignId }, { status });
  if (['active', 'inactive'].includes(status)) {
    const change = {
      changeContext: {
        campaignId: ObjectId(campaignId),
      },
      changeType:
        status === 'active'
          ? campaignChangeTypes.CAMPAIGN_ACTIVATED
          : campaignChangeTypes.CAMPAIGN_INACTIVATED,
      userContext: { userId, superadmin, superAdminName, superAdminEmail, role, loginId },
    };
    queueCampaignChange(change);
  }
  updateUserCaches(userId);
  return { success: true, message: '' };
};

const removeCampaign = async (_, { campaignId }, { userId }) => {
  campaignId = await idToMongoId(userId, campaignId);
  const campaign = await model(userId).findByIdAndUpdate(campaignId, {
    deleted: true,
  });
  updateUserCaches(userId);
  return !!campaign;
};

const updateCampaign = async (_, { campaignId, campaignState }, { userId }) => {
  campaignId = await idToMongoId(userId, campaignId);
  const campaign = await model(userId).findByIdAndUpdate(campaignId, campaignState, { new: true });
  updateUserCaches(userId);
  return campaign;
};

const addCampaignIntegration = async (_, { campaignId, input }, { userId }) => {
  campaignId = await idToMongoId(userId, campaignId);
  const { id, settings, bindings } = input;
  const CampaignModel = model(userId);

  const integrationId = ObjectId();
  const campaign = await CampaignModel.findOneAndUpdate(
    { _id: campaignId },
    {
      $push: {
        'settings.integrations': { _id: integrationId, id, settings, bindings },
      },
    },
    { returnDocument: 'after' },
  );

  await checkMissingBindings(userId, campaign);

  return integrationId;
};

const updateCampaignIntegration = async (_, { campaignId, input }, { userId }) => {
  campaignId = await idToMongoId(userId, campaignId);
  const { _id, id, settings, bindings } = input;
  const CampaignModel = model(userId);

  const campaign = await CampaignModel.findOneAndUpdate(
    { _id: campaignId, 'settings.integrations._id': _id },
    {
      $set: {
        'settings.integrations.$': {
          _id,
          id,
          settings,
          bindings,
        },
      },
    },
    { returnDocument: 'after' },
  );

  await checkMissingBindings(userId, campaign);

  return !!campaign;
};

const getCampaignsByIntegration = async (_, { integrationId }, { userId }) => {
  const campaigns = await model(userId).find(
    { 'settings.integrations.id': integrationId },
    { _id: 0, name: 1 },
  );
  return campaigns.map((c) => c.name);
};

const getCampaignsByIntegrationIds = async (_, { integrationIds }, { userId }) => {
  const campaignsCnt = {};

  await Promise.all(
    integrationIds.map(async (integrationId) => {
      const campaigns = await model(userId).find(
        { 'settings.integrations.id': integrationId },
        { _id: 0, status: 1 },
      );
      const activeCnt = campaigns.filter(({ status }) => status === 'active').length;
      campaignsCnt[integrationId] = { sum: campaigns.length, activeCnt };
    }),
  );
  return campaignsCnt;
};

const generateOldToken = async (_, { variantId }, { userId: jti, superadmin }) => {
  let crId;
  const campaign = await model(jti).findOne(
    { 'variants._id': ObjectId(variantId) },
    { id: 1, 'variants._id': 1, 'variants.id': 1 },
  );
  campaign.variants.forEach((variant) => {
    if (variant._id.equals(variantId)) crId = variant.id;
  });
  const token = await signToken(
    {
      jti,
      superadmin,
      caId: campaign.id,
      crId,
      aud: OM_ADMIN_URL,
    },
    {
      expiresIn: 3600,
      sharedKey: true,
    },
  );
  return `${OM_ADMIN_URL}/integrations/embedded/editor?token=${token}&embedded=1`;
};

const variantPreviews = async (_, { variantId }, { userId, log }) => {
  try {
    const {
      variants: [{ previewURLs, previewGenerated, previewGeneratedAt }],
    } = await model(userId).findOne({ 'variants._id': variantId }, { 'variants.$': 1 });

    return {
      previewURLs,
      previewGenerated,
      previewGeneratedAt,
    };
  } catch (e) {
    log.error(`variantPreviews failed for user: ${userId}, variant: ${variantId}`);
    throw e;
  }
};

const getOldVariantSettings = async (_, { campaignId, variantId }, { userId }) => {
  const { variants } = await model(userId).findOne(
    { id: campaignId, 'variants.id': variantId },
    { 'variants.$': 1 },
  );
  return variants[0].settings;
};

const activeCampaignsUsingFeatures = async (_, { features }, { userId }) => {
  const campaigns = await getActiveCampaignsUsingFeatures(userId, features);

  return campaigns.map((campaign) => ({
    ...campaign.toObject(),
    conversionRate:
      campaign.impressions > 0 ? (campaign.conversions / campaign.impressions).toFixed(2) : 0,
  }));
};

const toggleNotifyMeStatus = async (_, { campaignId, status }, { userId }) => {
  const campaign = await model(userId).findOne({ id: campaignId }, { 'settings.notifyMe': 1 });
  const settings = campaign.settings.notifyMe || {
    status: false,
    emails: [],
  };
  settings.status = status;
  await model(userId).update({ id: campaignId }, { $set: { 'settings.notifyMe': settings } });

  return { success: true, message: '' };
};

const addNotifyEmail = async (_, { input }, { userId, loginId }) => {
  const { campaign: campaignId, email } = input;
  let alreadyAdded = false;
  let notifyMeEntity = { email, status: 'pending', hash: null };
  const campaignModel = model(userId);
  const campWithSameNotifyEmail = await campaignModel.find(
    { 'settings.notifyMe.emails.email': email },
    { id: 1, name: 1, domainId: 1, 'settings.notifyMe.emails': 1 },
  );
  const campaign = await campaignModel.findOne(
    { id: campaignId },
    { 'settings.notifyMe': 1, name: 1, domainId: 1 },
  );

  if (campWithSameNotifyEmail.length) {
    campWithSameNotifyEmail.forEach((camp) => {
      camp.settings.notifyMe.emails.forEach((notifyMe) => {
        if (notifyMe.email === email) {
          notifyMeEntity.hash = notifyMe.hash;
          notifyMeEntity.status = notifyMe.status;
          if (!alreadyAdded && camp.id === campaign) {
            alreadyAdded = true;
          }
        }
      });
    });
  }

  if (!alreadyAdded) {
    notifyMeEntity.hash = notifyMeEntity.hash || randomstring.generate(20);
    const settings = campaign.settings.notifyMe || {
      status: false,
      emails: [],
    };
    settings.emails.push(notifyMeEntity);

    await campaignModel.update({ id: campaignId }, { $set: { 'settings.notifyMe': settings } });

    let account = await AccountModel.findOne({ databaseId: userId });
    const login = await LoginModel.findOne({ _id: loginId });

    if (account.type === 'sub') {
      account = await AccountModel.findOne({ subAccounts: account._id });
    }

    const domainsByIds = account.settings.domains.reduce((acc, curr) => {
      acc[curr._id.toString()] = curr.domain;
      return acc;
    }, {});

    const campaigns = campWithSameNotifyEmail.map((c) => ({
      name: c.name,
      domain: domainsByIds[c.domainId.toString()],
    }));

    campaigns.unshift({
      name: campaign.name,
      domain: domainsByIds[campaign.domainId.toString()],
    });

    sendInvitationEmail(
      login,
      email,
      encrypt(userId),
      notifyMeEntity.hash,
      { whiteLabelSettings: account.settings.whiteLabel },
      campaigns,
    );
  }

  return { success: true, entity: notifyMeEntity };
};

const removeNotifyMeEmail = async (_, { input }, { userId }) => {
  const { campaign, email } = input;
  const campaignModel = model(userId);

  await model(userId).update(
    { id: campaign, 'settings.notifyMe.emails.email': email },
    { $pull: { 'settings.notifyMe.emails': { email } } },
  );

  const campNotifyMeSettings = await campaignModel.findOne(
    { id: campaign },
    { id: 1, 'settings.notifyMe': 1 },
  );

  if (
    !campNotifyMeSettings.settings.notifyMe.emails.length &&
    campNotifyMeSettings.settings.notifyMe.status
  ) {
    await campaignModel.update({ id: campaign }, { $set: { 'settings.notifyMe.status': false } });
  }

  return { success: true, message: '' };
};

const validateNotifyMeInvitation = async (_, { userId, hash }) => {
  const databaseId = decrypt(userId);
  const campaign = await model(databaseId).findOne(
    { 'settings.notifyMe.emails.hash': hash },
    { 'settings.notifyMe.emails': 1 },
  );

  if (campaign) {
    const notify = campaign.settings.notifyMe.emails.filter((e) => e.hash === hash).pop();
    await model(databaseId).update(
      { 'settings.notifyMe.emails.email': notify.email },
      { $set: { 'settings.notifyMe.emails.$.status': 'accepted' } },
      { multi: true },
    );
  }

  return { success: true, message: '' };
};

const _setDomain = (campaigns, domains = []) => {
  for (const campaign of campaigns) {
    const domainObject = domains.find(({ _id }) => _id.equals(campaign.domainId));
    campaign.domain = domainObject?.domain;
  }
};

const getCampaignsForSegmentModal = async (_, { search, id }, { userId: databaseId }) => {
  let criteria = { id: { $ne: id } };

  if (search) {
    criteria.name = new RegExp(search, 'ui');
  }

  const [campaigns, { settings: { domains = [] } = {}, features = [] } = {}] = await Promise.all([
    model(databaseId)
      .find(criteria, {
        _id: 1,
        'settings.frontendRules': 1,
        name: 1,
        domainId: 1,
      })
      .sort({ _id: -1 }),
    AccountModel.findOne({ databaseId }, { 'settings.domains': 1, features: 1 }),
  ]);

  _setDomain(campaigns, domains);

  const isFrequencyEnabled = features.includes(FEATURES.FREQUENCY_RULE_V2);
  let resultCampaigns = campaigns;

  if (isFrequencyEnabled) {
    resultCampaigns = campaigns.filter((campaign) => {
      if (campaign?.settings?.frontendRules) {
        campaign.settings.frontendRules = campaign.settings.frontendRules.filter(
          (rule) => rule.type !== 'maximumPopupDisplay',
        );
        // filter out segments with no rules (All visitors)
        return campaign.settings.frontendRules.length > 0;
      }
      return true;
    });
  }

  return resultCampaigns;
};

const createDynamicContentCampaignFromCache = async (_, { input }, { userId, loginId, log }) => {
  const { name, url, addControlVariant, needCacheChanges } = input;
  log.info('Create DC campaign from cache', { userId, url, loginId });
  const domain = await findDomainByUrl(url, { userId, log });
  const domainId = `${domain?._id ?? ''}`;
  const campaign = await createDynamicContentCampaign(
    {
      name,
      domainId,
      templateId: undefined,
      dynamicContent: true,
      addControlVariant,
      featureMode: FEATURE_MODE_SAB,
    },
    userId,
    loginId,
    log,
  );

  const changes = needCacheChanges ? await getDynamicContentChangesFromCache(url) : [];

  log.info('Add changes to campaign', { userId, campaign: campaign.id, domainId });
  const changeDoc = await dynamicContentModel.create({
    databaseId: userId,
    campaignId: campaign.id,
    variantId: campaign.variants[0]._id,
    changes,
  });
  campaign.variants[0].changes = changeDoc._id;
  try {
    campaign.variants[0].lastEditUrl = new URL(url).href;
  } catch (err) {
    log.error('unable to set lastEditUrl', { err, userId, url });
  }
  campaign.variants[0].featureMode = FEATURE_MODE_SAB;
  await campaign.save();

  if (needCacheChanges) {
    await SmartABTestEvaluator.initNewSABChanges(changes, {
      databaseId: userId,
      campaignId: campaign.id,
      variantId: campaign.variants[0]._id,
    });
  }

  return {
    success: true,
    campaignId: campaign.id,
    variantId: campaign.variants[0]._id,
  };
};

const generateContent = async (_, { prompt }) => {
  const response = await OpenAIAdapter.getCompletions([{ text: prompt }]);
  return response[0].choices[0].message.content;
};

const getDashboardTaskListData = async (_, __, { userId }) => {
  const campaigns = await model(userId).find(
    { status: { $in: ['active', 'inactive'] } },
    { status: 1, id: 1 },
  );

  const firstInactive = campaigns?.find?.(({ status }) => status === 'inactive') ?? null;
  const allInactive = campaigns?.every?.(({ status }) => status === 'inactive') ?? true;

  return {
    campaignCount: campaigns?.length ?? 0,
    allInactive,
    firstInactive: firstInactive?.id ?? null, // numeric ID
  };
};

const hasAnyActiveOrInactiveCampaign = async (_, __, { userId }) => {
  const campaigns = await model(userId).find(
    { status: { $in: ['active', 'inactive'] } },
    { status: 1, id: 1 },
  );

  return !!campaigns?.length;
};

module.exports = {
  Query: {
    variant,
    allCampaigns,
    campaignList,
    allVariants,
    getCampaignSettings,
    getRecommendedIntegrations,
    getRecommendedIntegrationsState,
    getCampaign,
    getCampaignsByIntegration,
    getCampaignsByIntegrationIds,
    generateOldToken,
    variantPreviews,
    getOldVariantSettings,
    activeCampaignsUsingFeatures,
    validateNotifyMeInvitation,
    getCampaignAlerts,
    getCampaignsForSegmentModal,
    getActiveChanges,
    generateContent,
    getDashboardTaskListData,
    hasAnyActiveOrInactiveCampaign,
  },
  Mutation: {
    archiveCampaigns,
    toggleNotifyMeStatus,
    addNotifyEmail,
    removeNotifyMeEmail,
    changeCampaignStatus,
    changeVariantStatus,
    copyCampaign,
    copyVariant,
    createCampaign,
    createVariantFromTemplate,
    deleteCampaigns,
    deleteVariant,
    newVariant,
    removeCampaign,
    rejectRecommendedIntegration,
    renameVariant,
    saveCampaignSettings,
    changeCampaignSettings,
    updateCampaign,
    updateVariant,
    addCampaignIntegration,
    updateCampaignIntegration,
    updateTestRunningSetting,
    savePreview,
    restoreCampaigns,
    createControlVariant,
    deleteControlVariant,
    copyCampaignFromAnotherAccount,
    createDynamicContentCampaignFromCache,
  },
};
