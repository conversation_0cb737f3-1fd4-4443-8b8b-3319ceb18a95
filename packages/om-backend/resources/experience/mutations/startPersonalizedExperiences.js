const { model: ExperienceModel } = require('../experience.model');
const { model: CampaignModel } = require('../../campaign/campaign.model');
const { queueCampaignChange } = require('../../../services/queue/changeLogQueue');
const { campaignChangeTypes } = require('../../changeLog/common');
const { ObjectId } = require('../../account/account.model');
const { removeDeletedExperiences } = require('../helpers/removes');

const startPersonalizedExperiences = async (_, { campaign: rawCampaignId }, userContext) => {
  const {
    userId: databaseId,
    superadmin,
    superAdminName,
    superAdminEmail,
    role,
    log,
    loginId,
  } = userContext;
  const userCampaignModel = CampaignModel(databaseId);
  try {
    const campaign = ObjectId(rawCampaignId);
    const existingExperiences = await ExperienceModel.countDocuments({
      campaign,
      deletedAt: { $exists: false },
    });

    const alreadyPersonalized = !!existingExperiences;

    if (alreadyPersonalized) return { success: true };
    const userCampaign = await userCampaignModel.findOne({ _id: campaign });
    const variantIds = userCampaign.variants
      .filter((variant) => variant.status !== 'deleted')
      .map((variant) => variant._id);

    const experienceA = new ExperienceModel({
      name: `Experience A`,
      campaign,
      databaseId,
      priority: 0,
      variants: variantIds,
    });

    const experienceB = new ExperienceModel({
      name: `Experience B`,
      campaign,
      databaseId,
      priority: 1,
    });

    queueCampaignChange([
      {
        changeContext: {
          campaignId: campaign,
          experienceId: experienceA._id,
        },
        changeType: campaignChangeTypes.ADDED_EXPERIENCE,
        userContext: {
          userId: databaseId,
          superadmin,
          superAdminName,
          superAdminEmail,
          role,
          loginId,
        },
      },
      {
        changeContext: {
          campaignId: campaign,
          experienceId: experienceB._id,
        },
        changeType: campaignChangeTypes.ADDED_EXPERIENCE,
        userContext: {
          userId: databaseId,
          superadmin,
          superAdminName,
          superAdminEmail,
          role,
          loginId,
        },
      },
    ]);

    await Promise.all([
      removeDeletedExperiences({ campaign, databaseId }),
      experienceA.save(),
      experienceB.save(),
    ]);
  } catch (error) {
    log.error({ error }, 'Failed to start personalized experiences!');
    return { success: false };
  }

  return { success: true };
};

module.exports = {
  startPersonalizedExperiences,
};
