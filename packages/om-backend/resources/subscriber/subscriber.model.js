const mongoose = require('mongoose');
const { secondaryConnection } = require('../../services/db/connection');

const Schema = mongoose.Schema;

const SubscriberSchema = new Schema(
  {
    id: { type: Number },
    firstName: { type: String, required: false },
    lastName: { type: String, required: false },
    campaignId: { type: Schema.Types.ObjectId },
    variantId: { type: Schema.Types.ObjectId },
    email: { type: String, required: false, sparse: true },
    syncStatus: { type: String, default: null },
    deleted: { type: Boolean, default: false },
    numberOfFills: { type: Number, default: 1 },
    customFields: { type: Object },
    pageUserId: { type: String },
    databaseId: { type: Number, required: false },
    canAppearOnLeadsPage: { type: Boolean, required: false },
  },
  { timestamps: true },
);

const subscriberModel = mongoose.model('Subscriber', SubscriberSchema, 'user_subscribers');
const subscriberModelSecondary = secondaryConnection.model(
  'Subscriber',
  SubscriberSchema,
  'user_subscribers',
);

module.exports = {
  model: subscriberModel,
  secondary: subscriberModelSecondary,
  ObjectId: mongoose.Types.ObjectId,
};
