const {
  upsertCouponConfig,
  removeCouponConfig,
  removeCouponConfigsForVariant,
} = require('../../services/couponConfig');

const setAutomaticCouponConfig = async (
  _,
  { type, prefix, value, expiration, expirationType, expiresAt, expiresIn, variantId, elementId },
  { userId },
) => {
  await upsertCouponConfig({
    type,
    prefix,
    value,
    expiration,
    expirationType,
    expiresAt,
    expiresIn,
    variantId,
    elementId,
    userId,
  });
  return { success: true };
};

const removeAutomaticCouponConfig = async (_, { variantId, elementId }, { userId }) => {
  await removeCouponConfig({ variantId, elementId, userId });
  return { success: true };
};

const removeAutomaticCouponConfigForVariant = async (_, { variantId }, { userId }) => {
  await removeCouponConfigsForVariant({ variantId, userId });
  return { success: true };
};

module.exports = {
  Mutation: {
    setAutomaticCouponConfig,
    removeAutomaticCouponConfig,
    removeAutomaticCouponConfigForVariant,
  },
};
