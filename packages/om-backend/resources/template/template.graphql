enum TemplateType {
  popup
  nanobar
  sidebar
  fullscreen
  interstitial
  embedded
  gamification
  survey
  all
}

type Template {
  _id: String!
  name: String!
  displayName: String
  template: String
  type: TemplateType
  overlayColor: String
  previewUrls: [String]
  previewGeneratedAt: String
  contentUrl: String
  goals: [String]
  categories: [String]
  status: String
  seasonal: Boolean
  order: Int
  locale: String
  agencySettings: JSON
  author: JSON
  users: JSON
  conversionRate: Float
  useCase: String
  universal: Boolean
  theme: String
  updatedAt: DateTime
}

type TemplateNames {
  _id: String!
  name: String!
}

type TemplateResponse {
  _id: String
  name: String
  type: String
  images: [String]
  goalTags: [String]
  previewUrl: String
}

type FilterResponse {
  name: String
  count: Int
}

type DuplicateResponse {
  success: Boolean
  message: String
  template: Template
}

type SeasonalBanner {
  key: String!
  period: Period!
  locale: [String!]!
  img: String!
}

type Period {
  start: DateTime!
  end: DateTime!
}
input SearchTemplateInput {
  _id: ID
  name: String
}

input UpsertTemplateInput {
  name: String
  template: String
}

input TemplateOrder {
  id: ID
  order: Int
}

type ConfigResponse {
  success: Boolean
  items: JSON
}

# type PublishedTemplates {
#   counts: JSON
#   templates: [Template]
# }

extend type Query {
  template(input: SearchTemplateInput!): Template
  allTemplatesByLocale(locale: String): [JSON]
  allTemplates(
    type: TemplateType
    goals: [String]
    categories: [String]
    locale: String
    users: [String]
    themes: [String]
    useCases: [String]
  ): JSON
  publishedTemplates(
    types: [TemplateType]
    goals: [String]
    categories: [String]
    sortBy: String
    search: String
  ): JSON
  templateCategories(types: [TemplateType], goals: [String]): [FilterResponse]
  templateGoals(types: [TemplateType], categories: [String]): [FilterResponse]
  templateTypes(goals: [String], categories: [String]): [FilterResponse]
  allTemplateGoals: ConfigResponse
  allTemplateThemes: ConfigResponse
  allTemplateCategories: ConfigResponse
  customTemplateId: ID
  flyerTemplateId: ID
  getTemplateThemes: JSON
  getTemplateTheme(name: String!): JSON
  getThemeTemplates(theme: String!, locale: String): JSON
  getTemplateTags: JSON
  getTemplateTagUsage(_id: ID!): Int
  getTemplateThemesWithMainTemplates: JSON
  getDummyTemplate: JSON
  getChooserFilteredTemplates(filter: JSON, type: String): JSON
  getChoosableThemeCategories: JSON
  availableTemplateLanguages: JSON
  getPossibleTemplateFilterValues: JSON
  getSortedSeasonsWithBanner: JSON
  getSeasonalBanner(locale: String): SeasonalBanner
}

extend type Mutation {
  upsertTemplate(input: UpsertTemplateInput!): Boolean!
  changeTemplateStatus(templateId: ID!, status: String!): Boolean!
  changeTemplate(
    templateId: ID!
    displayName: String
    name: String!
    goals: [String]
    categories: [String]
    users: [String]
    locale: String
    universal: JSON
    tags: [ID]
    favorite: Boolean
  ): Response!
  changeTemplateOrder(orders: [TemplateOrder]!): Boolean!
  toggleTemplateSeasonal(templateId: ID!, value: Boolean!): Boolean!
  duplicateTemplate(templateId: ID!, displayName: String, name: String!): DuplicateResponse!
  newTemplate(
    displayName: String
    name: String!
    goals: [String]
    locale: String
    categories: [String]
    users: [String]
    type: String
    universal: JSON
    favorite: Boolean
  ): Response!
  deleteTemplate(templateId: ID!): Response!
  tagTemplates(templateIds: [ID]!, tagIds: [ID]!): Boolean!
  addTemplateTag(_id: ID!, name: String!): JSON
  removeTemplateTag(_id: ID!): Boolean!
  changeTemplateTag(_id: ID!, enName: String, huName: String): Boolean!
  toggleTemplatePartnerFeatured(templateId: ID!): Boolean!
}
