const crypto = require('crypto');
const _get = require('lodash.get');
const { languages } = require('@om/template-properties/src/translate/constants');
const { applyCustomStyleToTemplate } = require('@om/custom-theme-styles/src/index');
const { model } = require('./template.model');
const { model: configModel } = require('../config/config.model');
const { model: LoginModel } = require('../login/login.model');
const { model: AccountModel, ObjectId } = require('../account/account.model');
const { model: UseCaseModel } = require('../useCase/useCase.model');
const { model: CustomThemeModel } = require('../custom_theme/custom_theme.model');
const { model: SearchModel } = require('../template_fuzzy_search/template_fuzzy_search.model');
const {
  model: TemplateCollectionModel,
} = require('../template_collection/template_collection.model');
const {
  uploadAndSavePreviewsForTemplate,
  isGamificationTemplate,
  isSurveyTemplate,
} = require('../../util/templateUtil');
const logger = require('../../logger');
const redisClient = require('../../services/ioRedisAdapter');
const { canManageTemplates } = require('../../util/templateUtil');
const {
  mapUseCasesByID,
  TEMPLATE_STATUS,
  getTemplateQuery,
  getFilteredTemplates,
  getThemes,
  getSortedSeasons,
  buildCategoryFilter,
  buildThemeKitFilter,
  buildTypeFilter,
  resolveTemplateByType,
  findActiveOrClosestTemplate,
  FILTER_KEY_FULLSCREEN,
} = require('./helpers');
const { uploadBase64CropImages } = require('../../services/imageService');
const { redis } = require('../../services/ioRedisAdapter');
const { themeTemplateProjection } = require('./helpers/projections');

const FLYER_TEMPLATE_NAME = 'LOsgBOG2D';

// resolvers
const template = async (_, { input }, { userId: databaseId }) => {
  let query = {};
  if (input._id) query = { _id: input._id };
  if (input.name) query = { name: input.name };
  const template = await model.findOne(query);
  const themeKitId = template?.template?.themeKit?.id;
  const hasThemeKitInTemplate = themeKitId !== null;
  if (!hasThemeKitInTemplate) {
    return template;
  }

  const customTheme = await CustomThemeModel.findOne({
    databaseId,
    _id: ObjectId(themeKitId),
  });
  if (!customTheme) {
    return template;
  }

  applyCustomStyleToTemplate(template.template, customTheme);

  return template;
};

const allTemplatesByLocale = async (_, { locale }, { userId: databaseId, log }) => {
  log.info('[UFILTERED TPLS] GET', { databaseId });
  const id = await canManageTemplates(databaseId);

  if (!id) {
    log.info('[UFILTERED TPLS] NO PERMISSION', { databaseId });
    return [];
  }

  return model.templateAggregation({ locale }, { id, databaseId }, [], false, true);
};

const allTemplates = async (
  _,
  { type, goals, categories, locale, users, themes, useCases },
  { userId },
) => {
  const id = await canManageTemplates(userId);
  if (!id) return false;

  const query = await getTemplateQuery({
    type,
    goals,
    categories,
    locale,
    onlyPublished: false,
    users,
    themes,
    useCases,
    userId,
  });

  const filteredTemplates = await getFilteredTemplates({
    query,
    author: { id, databaseId: userId },
    types: [type],
    goals,
    categories,
    userLocale: locale,
    templateOperations: [],
    includeDrafts: true,
  });

  return filteredTemplates;
};

const upsertTemplate = async (_, { input }, { userId }) => {
  if (!(await canManageTemplates(userId))) return false;

  const { name, template } = input;
  const isWheel = template.indexOf('"type":"OmLuckyWheel"') > -1;
  const parsedTemplate = JSON.parse(template);

  if (parsedTemplate.themeKit?.id && typeof parsedTemplate.themeKit.id !== 'object') {
    parsedTemplate.themeKit.id = ObjectId(parsedTemplate.themeKit.id);
  }

  await uploadBase64CropImages(userId, parsedTemplate.uid, parsedTemplate);

  const type = getTemplateType(parsedTemplate, isWheel);
  const gamification = isGamificationTemplate(template);
  const survey = isSurveyTemplate(template);
  await model.findOneAndUpdate(
    { name },
    {
      $set: { name, template: parsedTemplate, type, gamification, survey },
      $setOnInsert: { status: 'draft' },
    },
    { upsert: true },
  );

  uploadAndSavePreviewsForTemplate({ userId, name });

  return true;
};

const getTemplateType = (t, isWheel = false) => {
  const mode = _get(t, 'style.mode');
  if (mode === 'nano') return 'nanobar';
  if (mode === 'sidebar') return 'sidebar';
  if (mode === 'interstitial' || isWheel) return 'interstitial';
  if (mode === 'embedded') return 'embedded';
  if (mode === 'fullscreen') return 'fullscreen';
  if (mode === 'popup') {
    const position = _get(t, 'style.overlay.position');
    if (position !== 5) return 'sidebar';
  }
  return 'popup';
};

const changeTemplateStatus = async (_, { templateId, status }, { userId }) => {
  const id = await canManageTemplates(userId);
  if (!id) return false;

  logger.info('Template status change start', {
    id,
    userId,
    templateId,
    newStatus: status,
  });
  const filter = { _id: templateId };
  let update = { status };

  if (userId !== 44) {
    const template = await model.findOne(filter);
    if (template) {
      const base = {
        id,
        databaseId: userId,
        status: template.status || 'draft',
        displayName: template.displayName,
        order: template.order || 500,
        goals: template.goals,
        categories: template.categories,
        users: [],
      };
      if (template.agencySettings) {
        const settingsIndex = template.agencySettings.findIndex((s) => s.databaseId === userId);
        if (settingsIndex !== -1) {
          const key = `agencySettings.${settingsIndex}.status`;
          update = {
            $set: {
              [key]: status,
            },
          };
        } else {
          update = {
            $push: {
              agencySettings: { ...base, status },
            },
          };
        }
      } else {
        update = {
          $set: {
            agencySettings: [{ ...base, status }],
          },
        };
      }
    }
  }

  try {
    const { modifiedCount } = await model.updateOne(filter, update);
    const result = modifiedCount !== 0;
    logger.info(result ? 'Template status change success' : 'Template status change failed', {
      id,
      userId,
      templateId,
      newStatus: status,
    });
    if (result) {
      await redisClient.del(`usecases:en:list`);
    }
    return result;
  } catch (e) {
    logger.error(e, 'Change template status error', {
      id,
      userId,
      templateId,
      newStatus: status,
    });
    return false;
  }
};

const changeTemplate = async (_, data, { userId }) => {
  const {
    templateId,
    displayName,
    name,
    goals,
    categories,
    users,
    locale,
    universal,
    tags,
    favorite,
  } = data;
  const id = await canManageTemplates(userId);
  if (!id) return { success: false, message: '' };

  const {
    name: existingName,
    useCase: existingUC,
    tags: existingTags,
  } = await model.findOne({ _id: templateId }, { name: 1, useCase: 1 });

  if (existingName !== name && (await isTemplateNameExistsAlready(name))) {
    return { success: false, message: 'templateNameExists' };
  }

  let update = {
    displayName,
    name,
    goals,
    categories,
    locale,
    tags,
    favorite,
    ...universal,
  };

  if (update.useCase) {
    update.useCase = ObjectId(update.useCase);

    await UseCaseModel.updateMany(
      { templates: ObjectId(templateId) },
      { $pull: { templates: ObjectId(templateId) } },
    );

    // Add to new one
    await UseCaseModel.updateOne(
      { _id: update.useCase },
      { $addToSet: { templates: [templateId] } },
    );
  } else {
    update.useCase = existingUC;
  }

  if (userId !== 44) {
    const template = await model.findOne({ _id: ObjectId(templateId) });
    const settingKeys = ['displayName', 'goals', 'categories', 'users'];
    if (template) {
      const base = {
        id,
        databaseId: userId,
        status: template.status || 'draft',
        displayName: template.displayName,
        order: template.order || 500,
        goals: template.goals,
        categories: template.categories,
        users: users || [],
      };
      if (template.agencySettings) {
        const settingsIndex = template.agencySettings.findIndex((s) => s.databaseId === userId);
        if (settingsIndex !== -1) {
          update = { $set: {} };
          settingKeys.forEach((prop) => {
            const key = `agencySettings.${settingsIndex}.${prop}`;
            update.$set[key] = data[prop];
          });
        } else {
          const agencySettings = { ...base };
          settingKeys.forEach((prop) => {
            agencySettings[prop] = data[prop];
          });
          update = { $push: { agencySettings } };
        }
      } else {
        const agencySettings = [{ ...base }];
        settingKeys.forEach((prop) => {
          agencySettings[0][prop] = data[prop];
        });
        update = { $set: { agencySettings } };
      }
    }
  }

  const { modifiedCount } = await model.updateOne({ _id: templateId }, update);

  if (existingName !== name) {
    uploadAndSavePreviewsForTemplate({ userId, name });
  }

  if (JSON.stringify(existingTags) !== JSON.stringify(tags)) {
    await redisClient.del(`usecases:en:list`);
  }

  return { success: modifiedCount !== 0, message: '' };
};

const changeTemplateOrder = async (_, { orders }, { userId }) => {
  const id = await canManageTemplates(userId);
  if (!id) return false;

  if (userId === 44) {
    const updates = orders.map((o) => ({
      updateOne: {
        filter: { _id: o.id },
        update: { $set: { order: o.order } },
      },
    }));

    logger.info('Change template defaults', updates);

    await model.bulkWrite(updates);
  } else {
    const templates = await model.find({}, { template: 0 });
    const updates = [];
    templates.forEach((t) => {
      const ordersIndex = orders.findIndex((o) => o.id === t._id.toString());
      const base = {
        id,
        databaseId: userId,
        displayName: t.displayName,
        status: t.status || 'draft',
        goals: t.goals,
        categories: t.categories,
        users: [],
      };
      if (ordersIndex === -1) return;
      const newOrder = orders[ordersIndex];
      if (t.agencySettings) {
        const oldSettingsIndex = t.agencySettings.findIndex(
          (settings) => settings.databaseId === userId,
        );
        if (oldSettingsIndex !== -1) {
          const key = `agencySettings.${oldSettingsIndex}.order`;
          updates.push({
            updateOne: {
              filter: { _id: t._id },
              update: { $set: { [key]: newOrder.order } },
            },
          });
        } else {
          updates.push({
            updateOne: {
              filter: { _id: t._id },
              update: {
                $push: {
                  agencySettings: {
                    ...base,
                    order: newOrder.order,
                  },
                },
              },
            },
          });
        }
      } else {
        updates.push({
          updateOne: {
            filter: { _id: t._id },
            update: {
              $set: {
                agencySettings: [
                  {
                    ...base,
                    order: newOrder.order,
                  },
                ],
              },
            },
          },
        });
      }
    });
    logger.info('Change template agency settings', updates);

    await model.bulkWrite(updates);
  }

  return true;
};

const toggleTemplateSeasonal = async (_, { templateId, value }, { userId }) => {
  if (!(await canManageTemplates(userId))) return false;

  const { modifiedCount } = await model.updateOne({ _id: templateId }, { seasonal: value });
  return modifiedCount !== 0;
};

const duplicateTemplate = async (_, { templateId, displayName, name }, { userId }) => {
  const id = await canManageTemplates(userId);
  if (!id) return { success: false, message: '' };
  if (await isTemplateNameExistsAlready(name))
    return { success: false, message: 'templateNameExists' };

  const template = await model.findOne({ _id: templateId });
  const author = { id, databaseId: userId };

  template.displayName = displayName;
  template.name = name;

  const duplicateTemplate = resetTemplateFields(template, author);

  await duplicateTemplate.save();
  uploadAndSavePreviewsForTemplate({ userId, name });
  return { success: true, message: '', template: duplicateTemplate };
};

const _typeMagic = (tpl, type) => {
  tpl.type = type;
  tpl.template.style.mode = type;

  if (type === 'sidebar') {
    tpl.template.style.overlay.position = 9;
  }

  if (type === 'nanobar') {
    tpl.template.style.mode = 'nano';
    tpl.template.style.overlay.position = 1;
  }

  return tpl;
};

const newTemplate = async (
  _,
  { displayName, name, goals, locale, categories, users, type, universal, favorite },
  { userId, log },
) => {
  const id = await canManageTemplates(userId);
  log.info('Try to create template', { databaseId: userId });
  if (!id) {
    log.error('No permission for template create', { databaseId: userId });
    return { success: false, message: '' };
  }
  if (await isTemplateNameExistsAlready(name)) {
    log.error('Template with this name already exists', {
      name,
      databaseId: userId,
    });
    return { success: false, message: 'templateNameExists' };
  }
  log.info('Copy custom template', {
    displayName,
    name,
    databaseId: userId,
  });

  let custom = await model.findOne({ name: 'Custom_hu' });

  log.info('Set attributes', {
    displayName,
    name,
    databaseId: userId,
    type,
  });

  custom.displayName = displayName;
  custom.name = name;
  custom.goals = goals;
  custom.categories = categories;
  custom.locale = locale;
  custom.favorite = favorite;

  if (universal) {
    if (universal.universal) {
      custom.universal = true;
    }
    custom.useCase = ObjectId(universal.useCase);
    custom.theme = universal.theme;
    custom.thumbnail = universal.thumbnail;
  }

  log.info('Set type related attributes', {
    displayName,
    name,
    databaseId: userId,
    type,
  });
  custom = _typeMagic(custom, type);

  log.info('Resetting fields', {
    displayName,
    name,
    databaseId: userId,
  });
  const author = { id, databaseId: userId };
  let mappedUsers;
  try {
    log.info('Map users ID to Object IDs', {
      displayName,
      name,
      databaseId: userId,
      type,
    });
    mappedUsers = users.map((u) => ObjectId(u));
  } catch (e) {
    logger.error('Map users ID to Object IDs', {
      given: users,
      displayName,
      name,
      databaseId: userId,
      type,
    });
  }
  const newTemplate = resetTemplateFields(custom, author, mappedUsers);

  if (custom.useCase) {
    // Update use case
    await UseCaseModel.updateOne(
      { _id: ObjectId(custom.useCase) },
      { $addToSet: { templates: [newTemplate._id] } },
    );
  }

  log.info('Save new template', {
    displayName,
    name,
    databaseId: userId,
    type,
  });
  await newTemplate.save();
  uploadAndSavePreviewsForTemplate({ userId, name });
  log.info('Saved successfully', {
    displayName,
    name,
    databaseId: userId,
    type,
  });
  return { success: true, message: '' };
};

const resetTemplateFields = (template, author, users) => {
  template._id = ObjectId();
  template.isNew = true;
  template.status = 'draft';
  template.seasonal = false;
  template.createdAt = new Date();
  template.updatedAt = new Date();
  template.author = author;

  if (author.databaseId !== 44) {
    template.agencySettings = {
      ...author,
      goals: template.goals || [],
      categories: template.categories || [],
      status: 'draft',
      order: template.order,
      users: users || [],
    };
  }

  return template;
};

const isTemplateNameExistsAlready = async (name) => {
  const count = await model.countDocuments({ name });
  return count !== 0;
};

const allTemplateCategories = async (_, __, { userId, log }) => {
  let result = { success: false, message: "Can't manage templates" };
  if (!(await canManageTemplates(userId))) return result;
  try {
    const cats = await configModel.findOne({ key: 'template_categories_by_period' }, { value: 1 });
    result = {
      success: true,
      items: cats.value.map((v) => v.key),
    };
  } catch (e) {
    result.message = "Can't get categories";
    log.error(e, 'Error while getting config');
  }
  return result;
};

const allTemplateGoals = async (_, __, { userId, log }) => {
  let result = { success: false, message: "Can't manage templates" };
  if (!(await canManageTemplates(userId))) return result;
  try {
    const goals = await configModel.findOne({ key: 'template_goals' }, { value: 1 });
    result = {
      success: true,
      items: goals.value,
    };
  } catch (e) {
    result.message = "Can't get goals";
    log.error(e, 'Error while getting config');
  }
  return result;
};

const allTemplateThemes = async (_, __, { userId, log }) => {
  let result = { success: false, message: "Can't manage templates" };
  if (!(await canManageTemplates(userId))) return result;
  try {
    const themes = await configModel.findOne({ key: 'template_themes' }, { value: 1 });
    const values = themes?.value?.map((e) => e.name);
    result = {
      success: true,
      items: values,
    };
  } catch (e) {
    result.message = "Can't get themes";
    log.error(e, 'Error while getting config');
  }
  return result;
};

const deleteTemplate = async (_, { templateId }, { userId, log }) => {
  const id = await canManageTemplates(userId);
  let result = { success: false, message: "Can't manage templates" };
  if (!id) return result;
  try {
    const _id = ObjectId(templateId);
    log.info('Delete template', { templateId, _id, userId });
    const filter = { _id };
    if (userId !== 44) {
      filter['author.databaseId'] = userId;
    }
    const delRes = await model.deleteOne(filter);
    log.info('Delete template result', { templateId, _id, userId, delRes });
    result = {
      success: !!delRes.n,
      message: !delRes.n ? 'No match found' : `Succesfully deleted (${delRes.n})`,
    };
  } catch (e) {
    result.message = "Can't delete template";
    log.error(e, 'Delete template failed', { templateId, userId });
  }
  return result;
};

const customTemplateId = async (_, __, { loginId }) => {
  const { locale } = await LoginModel.findOne({ _id: loginId }, { locale: 1 });
  const name = locale === 'hu' ? 'Custom_hu' : 'Custom';
  const { _id } = await model.findOne({ name });
  return _id;
};

const flyerTemplateId = async (_, __, { loginId }) => {
  const { _id } = await model.findOne({ name: FLYER_TEMPLATE_NAME });
  return _id;
};

const getTemplateThemes = getThemes;

const remapWithTemplate = (item, idKey, templates) => {
  if (!item[idKey]) {
    return null;
  }
  const templateId = item[idKey];

  item[idKey] = templates.find((template) => templateId.equals(template._id));
  return item[idKey] ? item : null;
};

const populateWithTemplates = async (items, idKey) => {
  const templateIds = items.map((item) => item[idKey]).filter((item) => !!item);
  const templates = await model.find({ _id: { $in: templateIds } }, themeTemplateProjection);
  return items.map((item) => remapWithTemplate(item, idKey, templates)).filter((item) => !!item);
};
const getTemplateThemesWithMainTemplates = async (...args) => {
  const themes = await getTemplateThemes(...args);
  const themesWithTemplate = themes.filter((theme) => theme.multicolor && theme.mainTemplate);
  return populateWithTemplates(themesWithTemplate, 'mainTemplate');
};

const getDummyTemplate = async () => {
  const dummyTemplate = await model.find({ dummy: true });
  return dummyTemplate || [];
};

const getTemplateTheme = async (...args) => {
  const themes = await getTemplateThemes(...args);
  const { name: themeName } = args[1];
  const theme = themes.find((theme) => theme.name === themeName);
  return theme;
};

const getThemeTemplates = async (_, { theme }) => {
  const themeTemplates = await model.find({
    theme,
    status: TEMPLATE_STATUS.published,
    'template.themeKit': { $exists: false },
  });
  const useCaseIds = themeTemplates.map(({ useCase = null }) => useCase).filter((item) => !!item);
  const useCases = await UseCaseModel.find(
    { _id: { $in: useCaseIds } },
    { events: 1, frontendRules: 1 },
  );

  const useCaseByID = mapUseCasesByID(useCases);

  themeTemplates.forEach(({ useCase = null }, index) => {
    if (useCase) themeTemplates[index].useCase = useCaseByID[`${useCase}`];
  });

  return themeTemplates || [];
};

const tagTemplates = async (_, { templateIds, tagIds }, { log }) => {
  try {
    await model.updateMany({ _id: { $in: templateIds } }, { $addToSet: { tags: tagIds } });
    await redisClient.del(`usecases:en:list`);
    log.info(`tagging ${templateIds.length} templates with ${tagIds.length} tags`);
    return true;
  } catch (e) {
    log.error(`error while tagging templates: ${e}`);
    return false;
  }
};

const getTemplateTags = async () => {
  const { value } = await configModel.findOne({ key: 'template_tags' });
  return value;
};

const addTemplateTag = async (_, { _id, name }, { log }) => {
  try {
    const newTag = {
      _id: ObjectId(_id),
      name: {
        en: name,
        hu: '',
      },
    };

    await configModel.updateOne({ key: 'template_tags' }, { $push: { value: newTag } });
    log.info(`adding new template tag ${name}`);
    return newTag;
  } catch (e) {
    log.error(`error while adding new template tag ${name}`);
  }
};

const getTemplateTagUsage = async (_, { _id }, { log }) => {
  try {
    const count = await model.countDocuments({ tags: ObjectId(_id) });
    logger.info(`successfully got template tag usage`);
    return count;
  } catch (e) {
    log.error(`error while getting template tag usage for tagId ${_id}`);
  }
};

const removeTemplateTag = async (_, { _id }, { log }) => {
  try {
    await configModel.updateOne(
      { key: 'template_tags' },
      { $pull: { value: { _id: ObjectId(_id) } } },
    );
    await model.updateMany({ tags: ObjectId(_id) }, { $pull: { tags: ObjectId(_id) } });
    await TemplateCollectionModel.updateMany(
      { tags: ObjectId(_id) },
      { $pull: { tags: ObjectId(_id) } },
    );
    await redisClient.del('template_collections:list');
    await redisClient.del(`usecases:en:list`);

    log.info(`removing template tag ${_id}`);
    return true;
  } catch (e) {
    log.error(`error while removing template tag ${_id}`);
  }
};

const changeTemplateTag = async (_, { _id, enName, huName }, { log }) => {
  const toSet = {};

  if (enName && enName.length) {
    toSet['value.$.name.en'] = enName;
  }

  if (huName && huName.length) {
    toSet['value.$.name.hu'] = huName;
  }

  // if (color && color.length) {
  //   toSet['value.$.color'] = color
  // }

  if (Object.keys(toSet).length) {
    try {
      await configModel.updateOne(
        { key: 'template_tags', 'value._id': ObjectId(_id) },
        { $set: toSet },
      );
      log.info(`changing template tag ${_id}`);
      return true;
    } catch (e) {
      log.info(`error while changing template tag ${e}`);
    }
  } else {
    return false;
  }
};

const toggleTemplatePartnerFeatured = async (_, { templateId }, { userId, log }) => {
  try {
    const template = await model.findOne({ _id: templateId }, { partnerFeatured: 1 });

    let partnerFeatured;

    if (template.partnerFeatured && template.partnerFeatured.includes(userId)) {
      partnerFeatured = template.partnerFeatured.filter((u) => u !== userId);
    } else {
      partnerFeatured = [...(template.partnerFeatured || []), userId];
    }

    await model.updateOne({ _id: templateId }, { $set: { partnerFeatured } });

    const {
      settings: {
        affiliate: { partnerInfo },
      },
    } = await AccountModel.findOne({ databaseId: userId }, { 'settings.affiliate.partnerInfo': 1 });

    if (partnerInfo && partnerInfo.slug) {
      await redisClient.del(`partner_collection:${partnerInfo.slug}`);
    }

    log.info('toggleTemplatePartnerFeatured SUCCESS');

    return true;
  } catch (e) {
    log.error(`toggleTemplatePartnerFeatured ERROR ${e}`);
    return false;
  }
};

const buildIndirectGoalsFilter = async ({ criteria, goals }) => {
  if (!goals?.length) return;
  const useCases = await UseCaseModel.find({ goals: { $in: goals } }, { _id: 1 });

  criteria.useCase = { $in: useCases.map(({ _id }) => _id) };
};

const buildContentFilter = ({ criteria, contents }) => {
  if (!contents?.length) return;
  const elementTypes = contents.map(
    (content) => `Om${content.charAt(0).toUpperCase()}${content.slice(1)}`,
  );

  criteria['template.elements.type'] = { $in: elementTypes };
};

const buildGeneralCriteria = async ({
  types,
  goals,
  contents,
  category,
  themeKit,
  search = null,
}) => {
  const criteria = { name: { $nin: ['Custom', 'Custom_hu'] } };
  await buildCategoryFilter({ criteria, category, search });
  buildTypeFilter({ criteria, types });
  buildThemeKitFilter({ criteria, themeKit });
  await buildIndirectGoalsFilter({ criteria, goals });
  buildContentFilter({ criteria, contents });

  return criteria;
};

const C_TTL_5_MIN = 300;
const C_KEY_FILTER_VALUES = 'templateChooser:filterValues-usecase';
const TEMPLATE_CONTENT_TYPES = [
  'coupon',
  'luckyWheel',
  'product',
  'countdown',
  'survey',
  'feedback',
  'pickAPresent',
  'scratchCard',
];
const SORTED_SEASONS_BANNER_KEY = 'sortedSeasonsBanner';
const SORTED_SEASONS_BANNER_TTL = 86400;
const SEASONAL_BANNER_BASE_KEY = 'seasonalBanner';

const getPossibleTemplateFilterValues = async () => {
  let values = await redisClient.get(C_KEY_FILTER_VALUES);

  try {
    if (values) return JSON.parse(values);
  } catch (e) {
    // Cannot parse json rebuild cache: see rest of the function
  }

  const [goals, types] = await Promise.all([
    UseCaseModel.distinct('goals', { 'templates.0': { $exists: true } }),
    model.distinct('type', { locale: 'en', status: 'published' }),
  ]);

  values = {
    goals: goals.filter((v) => !!v),
    types: types.filter((type) => type !== FILTER_KEY_FULLSCREEN),
    contents: TEMPLATE_CONTENT_TYPES,
  };

  await redisClient.setex(C_KEY_FILTER_VALUES, JSON.stringify(values), C_TTL_5_MIN);

  return values;
};

const getSeasonalBanner = async (_, { locale }) => {
  if (!['hu', 'en'].includes(locale)) {
    logger.error(`Invalid locale: ${locale}`);
    return [];
  }

  let cache = await redisClient.get(`${SEASONAL_BANNER_BASE_KEY}_${locale}`);

  if (cache) {
    return JSON.parse(cache);
  }

  const now = new Date().getTime();

  const banner = await findActiveOrClosestTemplate(locale, now);

  if (!banner) {
    logger.error('Cannot find banner for seasonal!');
    throw new Error('Cannot find banner for seasonal!');
  }

  await redisClient.setex(
    `${SEASONAL_BANNER_BASE_KEY}_${locale}`,
    JSON.stringify(banner),
    SORTED_SEASONS_BANNER_TTL,
  );
  return banner;
};

const getSortedSeasonsWithBanner = async () => {
  let cache = await redisClient.get(SORTED_SEASONS_BANNER_KEY);

  try {
    if (cache) return JSON.parse(cache);
  } catch (e) {
    // Cannot parse json rebuild cache: see rest of the function
  }

  const config = await configModel.findOne({ key: 'template_categories_banners' });
  const bannersWithImages = config.value;

  const seasons = await getSortedSeasons();
  const sortedTemplates = bannersWithImages.sort((a, b) => {
    const indexA = seasons.indexOf(a.name);
    const indexB = seasons.indexOf(b.name);

    if (indexA === -1 && indexB === -1) return 0;
    if (indexA === -1) return 1;
    if (indexB === -1) return -1;

    return indexA - indexB;
  });

  await redisClient.setex(
    SORTED_SEASONS_BANNER_KEY,
    JSON.stringify(sortedTemplates),
    SORTED_SEASONS_BANNER_TTL,
  );

  return sortedTemplates;
};

const getManagerDetails = async (databaseId, type) => {
  if (type !== 'sub') return null;
  const account = await AccountModel.findOne({ databaseId }, { _id: 1 });
  if (!account?._id) return null;
  const manager = await AccountModel.findOne(
    {
      type: 'agency',
      subAccounts: { $in: [account._id] },
    },
    { databaseId: 1 },
  );

  return { id: manager._id, databaseId: manager.databaseId };
};

const getCacheKey = (filter) => {
  try {
    const filterCacheKey = crypto
      .createHash('sha1')
      .update(JSON.stringify(filter))
      .digest('base64');
    return `filter:${filterCacheKey}:v2`;
  } catch (e) {
    return null;
  }
};

const FUZZY_MIN_CONFIDENCE = 40;
const getChooserFilteredTemplates = async (_, { filter, type }, { userId, log }) => {
  const manager = await canManageTemplates(userId);
  const managedBy = await getManagerDetails(userId, type);
  const filterCacheKey = getCacheKey(filter);
  const filterCacheValue = await redis.get(filterCacheKey);
  const canSeeCache = !manager && !managedBy;

  if (filterCacheValue && canSeeCache) {
    try {
      return JSON.parse(filterCacheValue);
    } catch (e) {
      // get value on the fly
    }
  }

  const criteria = await buildGeneralCriteria(filter);
  const preFilterScores = {};
  let preFilterResultIds = null;

  if (filter?.search) {
    preFilterResultIds = [];
    const [fuzzyResults, exactFuzzyResults] = await Promise.all([
      SearchModel.fuzzySearch({ query: filter.search, minSize: 3 }),
      SearchModel.fuzzySearch({ query: filter.search, minSize: 3, exact: true }),
    ]);
    const includesObjectId = (id) => preFilterResultIds.some((item) => item.equals(id));

    for (const result of [...fuzzyResults, ...exactFuzzyResults]) {
      const { confidenceScore, templateId } = result.toObject();

      if (confidenceScore > FUZZY_MIN_CONFIDENCE && !includesObjectId(templateId)) {
        preFilterResultIds.push(templateId);
        preFilterScores[templateId] = confidenceScore;
      }
    }
    delete criteria.theme;
  }

  try {
    const { _id: userObjectId } = await AccountModel.findOne({ databaseId: userId }, { _id: 1 });
    const ctx = { filter, type, userId, log, manager, managedBy, userObjectId };
    const resolverType = manager && ['agency', 'normal'].includes(type) ? 'agency' : type;
    const resolver = manager ? resolveTemplateByType[resolverType] : resolveTemplateByType[type];
    const results = (await resolver?.(criteria, ctx, model, preFilterResultIds)) || {};

    const uniqueTemplateList = Object.values(
      [
        ...Object.values(results?.byTheme ?? {}).flat(),
        ...Object.values(results?.byCategory ?? {}).flat(),
      ].reduce((map, current) => {
        map[current._id] = current;
        return map;
      }, {}),
    );

    uniqueTemplateList.sort((a, b) => {
      const aPopularity = a?.popularity ?? 0;
      const bPopularity = b?.popularity ?? 0;

      if (a.favorite && b.favorite) return 0;

      if (a.favorite) return Number.MIN_SAFE_INTEGER;
      if (b.favorite) return Number.MAX_SAFE_INTEGER;

      return bPopularity - aPopularity;
    });

    results.sortOrder = uniqueTemplateList.map(({ _id }) => _id);

    Object.entries(results.byCategory).forEach(([key, templateList]) => {
      results.byCategory[key] = uniqueTemplateList.filter((tpl) =>
        templateList.some(({ _id }) => tpl._id === _id),
      );
    });

    Object.entries(results.byTheme).forEach(([key, templateList]) => {
      results.byTheme[key] = uniqueTemplateList.filter((tpl) =>
        templateList.some(({ _id }) => tpl._id === _id),
      );
    });

    if (filter?.search) {
      let all = Object.values(results.byTheme).flat();
      for (const tpl of all) {
        tpl.score = preFilterScores[tpl._id] ?? null;
      }

      results.searchResults = uniqueTemplateList.filter(({ _id }) =>
        all.some((tpl) => tpl._id === _id),
      );
    }

    if (canSeeCache && filter && filterCacheKey) {
      await redis.setex(filterCacheKey, 3600, JSON.stringify(results));
    }

    return results;
  } catch (e) {
    log.error(`Cannot resolve templates for ${userId} ${e}`, e);
  }

  return {};
};

const getChoosableThemeCategories = async (_, __, { userId: databaseId }) => {
  const [user, base, seasons, config] = await Promise.all([
    CustomThemeModel.find(
      {
        databaseId,
        sourceTheme: { $exists: true },
        hidden: { $ne: true },
      },
      { name: 1, sourceTheme: 1, themeKit: 1, logo: 1 },
    )
      .sort({ _id: -1 })
      .lean(),
    CustomThemeModel.find(
      {
        databaseId: 44,
        sourceTheme: { $exists: false },
        mainTemplate: { $exists: true },
        hidden: { $ne: true },
      },
      { name: 1, themeKit: 1, logo: 1 },
    )
      .sort({ _id: -1 })
      .lean(),
    getSortedSeasons(),
    configModel.findOne({ key: 'template_themes' }),
  ]);

  let baseThemes = [...base];
  if (databaseId !== 44) {
    const baseThemesWithTemplates = [];
    for (const baseTheme of base) {
      const templateCount = await model.countDocuments({
        'template.themeKit.name': baseTheme.name,
        status: 'published',
      });
      if (templateCount) {
        baseThemesWithTemplates.push(baseTheme);
      }
    }
    baseThemes = baseThemesWithTemplates;
  }

  baseThemes = baseThemes.map((theme) => ({
    ...theme,
    thumbnailV2: config.value.find(({ name }) => theme.name === name)?.thumbnailV2,
  }));

  return {
    user,
    base: baseThemes,
    seasons,
  };
};

const availableTemplateLanguages = async () => {
  return { en: 'English', ...languages };
};

module.exports = {
  Query: {
    allTemplates,
    allTemplatesByLocale,
    template,
    allTemplateCategories,
    allTemplateGoals,
    allTemplateThemes,
    customTemplateId,
    flyerTemplateId,
    getTemplateThemes,
    getTemplateTheme,
    getThemeTemplates,
    getTemplateTags,
    getTemplateTagUsage,
    getTemplateThemesWithMainTemplates,
    getDummyTemplate,
    getChooserFilteredTemplates,
    getChoosableThemeCategories,
    availableTemplateLanguages,
    getPossibleTemplateFilterValues,
    getSortedSeasonsWithBanner,
    getSeasonalBanner,
  },
  Template: {
    template: (obj) => JSON.stringify(obj.template),
  },
  Mutation: {
    upsertTemplate,
    changeTemplateStatus,
    changeTemplate,
    changeTemplateOrder,
    toggleTemplateSeasonal,
    duplicateTemplate,
    newTemplate,
    deleteTemplate,
    tagTemplates,
    addTemplateTag,
    removeTemplateTag,
    changeTemplateTag,
    toggleTemplatePartnerFeatured,
  },
};
