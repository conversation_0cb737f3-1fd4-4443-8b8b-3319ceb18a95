const moment = require('moment');
const _merge = require('lodash.merge');
const { model: AccountModel, ObjectId } = require('../../account/account.model');
const Template = require('../template.model');
const { model: configModel } = require('../../config/config.model');
const { model: CustomThemeModel } = require('../../custom_theme/custom_theme.model');
const redisClient = require('../../../services/ioRedisAdapter');
const resolveTemplateByType = require('./resolvers');

const TEMPLATE_STATUS = {
  draft: 'draft',
  published: 'published',
  unlisted: 'unlisted',
};

const TEMPLATE_LOCALE = 'en';

const CACHE_TTL_1_HOUR = 60 * 60;
const CACHE_TTL_1_DAY = 24 * CACHE_TTL_1_HOUR;

const FILTER_KEY_FULLSCREEN = 'fullscreen';

const mapUseCasesByID = (useCasesArray) => {
  const useCasesByID = {};

  (useCasesArray || []).forEach((useCase) => {
    useCasesByID[`${useCase._id}`] = useCase;
  });

  return useCasesByID;
};

const getTemplateQuery = async ({
  onlyPublished = false,
  accessUnpublished = false,
  users = [],
  themes = [],
  useCases = [],
  userId,
}) => {
  let query = { locale: TEMPLATE_LOCALE };

  if (onlyPublished) query.status = TEMPLATE_STATUS.published;
  else if (!onlyPublished && accessUnpublished) {
    query.status = { $in: [TEMPLATE_STATUS.published, TEMPLATE_STATUS.unlisted] };
  }
  if (themes.length > 0) {
    query.theme = { $in: themes };
  }

  if (notEmptyArray(useCases)) {
    query.useCase = { $in: useCases.map((id) => ObjectId(id)) };
  }

  if (notEmptyArray(users)) {
    const usersOr = [
      { users: { $exists: false } },
      { users: [] },
      { users: { $in: users.map((u) => ObjectId(u)) } },
    ];
    if (query.$or) {
      query.$and = [{ $or: [...query.$or] }, { $or: usersOr }];
      delete query.$or;
    } else {
      query.$or = usersOr;
    }
  }

  if (userId !== 44) {
    const universalFilter = [{ universal: { $exists: false } }, { universal: false }];

    query = queryAddition(query, universalFilter);
  }

  return query;
};

const queryAddition = (query, newCriteria) => {
  if (query.$and) {
    query.$and.push({ $or: newCriteria });
  } else if (query.$or) {
    query.$and = [{ $or: [...query.$or] }, { $or: newCriteria }];
  } else {
    query.$or = newCriteria;
  }

  return query;
};

const getNormalTemplateQuery = async ({ accessUnlisted }) => {
  const status = accessUnlisted
    ? { $in: [TEMPLATE_STATUS.published, TEMPLATE_STATUS.unlisted] }
    : TEMPLATE_STATUS.published;
  const query = {
    locale: TEMPLATE_LOCALE,
    status,
    $or: [{ author: { $exists: false } }, { 'author.databaseId': 44 }],
  };

  return query;
};

const getAuthorAndSubAccount = async (databaseId) => {
  if (databaseId === 44) {
    return { author: { databaseId: 44 }, subAccount: null };
  }

  const account = await AccountModel.findOne({ databaseId }, { type: 1 });
  let author = { databaseId: 44 };
  let subAccount = null;

  if (account && account.type === 'sub') {
    subAccount = account._id;
    const agency = await AccountModel.findOne({ subAccounts: account._id }, { databaseId: 1 });

    if (agency) {
      author = {
        id: agency._id,
        databaseId: agency.databaseId,
      };
    }
  } else if (account.type === 'agency') {
    author = {
      id: account._id,
      databaseId: account.databaseId,
    };
  }

  return { author, subAccount };
};

const notEmptyArray = (data) => data && data.length !== 0 && data[0] !== null;

const mergeWithCounts = function (all, withCounts) {
  const namesOfWithCounts = withCounts.map((i) => i.name);
  const missingWithCounts = all
    .filter((i) => !namesOfWithCounts.includes(i))
    .map((i) => ({ name: i, count: 0 }));

  const allWithCounts = withCounts
    .concat(missingWithCounts)
    .sort((a, b) => a.name.localeCompare(b.name));

  return allWithCounts;
};

const TPL_THEME_SEND_TO_BACK = ['corona', 'kata'];

const getFacet = ({ types, goals, categories }, templateOperations) => {
  return {
    $facet: {
      types: [
        { $match: templateFilterQuery({ goals, categories }) },
        { $group: { _id: '$type', count: { $sum: 1 } } },
        { $project: { _id: 0, name: '$_id', count: 1 } },
        { $sort: { name: 1 } },
      ],
      gamification: [
        { $match: templateFilterQuery({ goals, categories }) },
        {
          $group: {
            _id: 1,
            gamification: { $sum: { $cond: [{ $eq: ['$gamification', true] }, 1, 0] } },
          },
        },
        { $project: { _id: 0 } },
      ],
      survey: [
        { $match: templateFilterQuery({ goals, categories }) },
        {
          $group: {
            _id: 1,
            survey: { $sum: { $cond: [{ $eq: ['$survey', true] }, 1, 0] } },
          },
        },
        { $project: { _id: 0 } },
      ],
      goals: [
        { $match: templateFilterQuery({ types, categories }) },
        { $unwind: '$goals' },
        { $group: { _id: '$goals', count: { $sum: 1 } } },
        { $project: { _id: 0, name: '$_id', count: 1 } },
        { $sort: { name: 1 } },
      ],
      categories: [
        { $match: templateFilterQuery({ types, goals }) },
        { $unwind: '$categories' },
        { $group: { _id: '$categories', count: { $sum: 1 } } },
        { $project: { _id: 0, name: '$_id', count: 1 } },
        { $sort: { name: 1 } },
      ],
      templates: [
        ...templateOperations,
        { $match: templateFilterQuery({ types, goals, categories }) },
      ],
    },
  };
};

const getFilteredTemplates = async ({
  query,
  author,
  types,
  goals,
  categories,
  templateOperations,
  userLocale,
  includeDrafts = false,
}) => {
  const [filteredTemplates] = await Template.model.templateAggregation(
    query,
    author,
    [getFacet({ types, goals, categories }, templateOperations)],
    includeDrafts,
  );

  const configs = await configModel.find({
    key: { $in: ['template_goals', 'template_categories_by_period'] },
  });

  configs.forEach((c) => {
    if (c.key === 'template_goals') {
      filteredTemplates.goals = mergeWithCounts(c.value, filteredTemplates.goals);
    } else {
      const countByCategory = filteredTemplates.categories.reduce((acc, curr) => {
        return {
          ...acc,
          [curr.name]: curr.count,
        };
      }, {});

      const categories = [];

      c.value.forEach(({ key, period, locale }) => {
        if (locale.includes(userLocale)) {
          if (Object.keys(countByCategory).includes(key)) {
            categories.push({ name: key, period, count: countByCategory[key] });
          } else {
            categories.push({ name: key, period, count: 0 });
          }
        }
      });

      // year doesn't matter, so we set it to a constant leap year (2000),
      // so date comparison will work as expected
      const now = moment().set('year', '2000');

      const inProgressCategories = [];
      const otherCategories = [];

      for (const category of categories) {
        if (TPL_THEME_SEND_TO_BACK.includes(category.name)) {
          otherCategories.push(category);
          continue;
        }
        const isInProgress =
          moment(category.period.start).isBefore(now) && moment(category.period.end).isAfter(now);
        if (isInProgress) {
          inProgressCategories.push(category);
        } else {
          otherCategories.push(category);
        }
      }

      inProgressCategories.sort((a, b) => a.period.start - b.period.start);
      otherCategories.sort((a, b) => {
        if (TPL_THEME_SEND_TO_BACK.includes(a.name) || TPL_THEME_SEND_TO_BACK.includes(b.name)) {
          return Number.MAX_SAFE_INTEGER;
        }
        return a.period.start - b.period.start;
      });

      let nextCategoryIndex;
      for (let i = 0; i < otherCategories.length; i++) {
        if (moment(otherCategories[i].period.start).isAfter(now)) {
          nextCategoryIndex = i;
          break;
        }
      }

      filteredTemplates.categories = [
        ...inProgressCategories,
        ...otherCategories.slice(nextCategoryIndex),
        ...otherCategories.slice(0, nextCategoryIndex),
      ];
    }
  });

  filteredTemplates.types = mergeWithCounts(
    ['interstitial', 'nanobar', 'popup', 'sidebar'],
    filteredTemplates.types,
  );

  if (filteredTemplates.gamification && filteredTemplates.gamification.length) {
    filteredTemplates.types.push({
      name: 'gamification',
      count: filteredTemplates.gamification[0].gamification,
    });
  }

  if (filteredTemplates.survey && filteredTemplates.survey.length) {
    filteredTemplates.types.push({
      name: 'survey',
      count: filteredTemplates.survey[0].survey,
    });
  }

  return filteredTemplates;
};

const getNormalFilteredTemplates = async ({
  query,
  types,
  goals,
  categories,
  templateOperations,
  userLocale,
  includeDrafts = false,
}) => {
  const [filteredTemplates] = await Template.model.normalTemplateAggregation(
    query,
    [getFacet({ types, goals, categories }, templateOperations)],
    includeDrafts,
  );

  const configs = await configModel.find({
    key: { $in: ['template_goals', 'template_categories_by_period'] },
  });

  configs.forEach((c) => {
    if (c.key === 'template_goals') {
      filteredTemplates.goals = mergeWithCounts(c.value, filteredTemplates.goals);
    } else {
      const countByCategory = filteredTemplates.categories.reduce((acc, curr) => {
        return {
          ...acc,
          [curr.name]: curr.count,
        };
      }, {});

      const categories = [];

      c.value.forEach(({ key, period, locale }) => {
        if (locale.includes(userLocale)) {
          if (Object.keys(countByCategory).includes(key)) {
            categories.push({ name: key, period, count: countByCategory[key] });
          } else {
            categories.push({ name: key, period, count: 0 });
          }
        }
      });

      // year doesn't matter, so we set it to a constant leap year (2000),
      // so date comparison will work as expected
      const now = moment().set('year', '2000');

      const inProgressCategories = [];
      const otherCategories = [];

      for (const category of categories) {
        if (TPL_THEME_SEND_TO_BACK.includes(category.name)) {
          otherCategories.push(category);
          continue;
        }
        const isInProgress =
          moment(category.period.start).isBefore(now) && moment(category.period.end).isAfter(now);
        if (isInProgress) {
          inProgressCategories.push(category);
        } else {
          otherCategories.push(category);
        }
      }

      inProgressCategories.sort((a, b) => a.period.start - b.period.start);
      otherCategories.sort((a, b) => {
        if (TPL_THEME_SEND_TO_BACK.includes(a.name) || TPL_THEME_SEND_TO_BACK.includes(b.name)) {
          return Number.MAX_SAFE_INTEGER;
        }
        return a.period.start - b.period.start;
      });

      let nextCategoryIndex;
      for (let i = 0; i < otherCategories.length; i++) {
        if (moment(otherCategories[i].period.start).isAfter(now)) {
          nextCategoryIndex = i;
          break;
        }
      }

      filteredTemplates.categories = [
        ...inProgressCategories,
        ...otherCategories.slice(nextCategoryIndex),
        ...otherCategories.slice(0, nextCategoryIndex),
      ];
    }
  });

  filteredTemplates.types = mergeWithCounts(
    ['interstitial', 'nanobar', 'popup', 'sidebar'],
    filteredTemplates.types,
  );

  if (filteredTemplates.gamification && filteredTemplates.gamification.length) {
    filteredTemplates.types.push({
      name: 'gamification',
      count: filteredTemplates.gamification[0].gamification,
    });
  }

  if (filteredTemplates.survey && filteredTemplates.survey.length) {
    filteredTemplates.types.push({
      name: 'survey',
      count: filteredTemplates.survey[0].survey,
    });
  }

  return filteredTemplates;
};

const POPUP_TYPES = ['interstitial', 'popup'];

const templateFilterQuery = ({ types, goals, categories }) => {
  const query = {};

  if (types && types.length) {
    if (types.includes('gamification')) {
      query.gamification = true;
      types = types.filter((t) => t !== 'gamification');
    }

    if (types.includes('survey')) {
      query.survey = true;
      types = types.filter((t) => t !== 'survey');
    }
  }

  if (types === 'gamification') {
    query.gamification = true;
    types = '';
  }

  if (types === 'survey') {
    query.survey = true;
    types = '';
  }

  if (notEmptyArray(types)) {
    query.type = { $in: types };
  } else if (!Array.isArray(types) && types) {
    query.type = POPUP_TYPES.includes(types) ? { $in: [...POPUP_TYPES] } : types;
  }

  if (notEmptyArray(goals)) {
    query.goals = { $in: goals };
  }

  if (notEmptyArray(categories)) {
    query.categories = { $in: categories };
  }

  return query;
};

const getAccountFeatures = async (databaseId) => {
  let features = [];
  const account = await AccountModel.findOne({ databaseId }, { _id: 1, features: 1, type: 1 });

  if (account) {
    features = account.toObject().features || [];

    if (account.type === 'sub') {
      const agency = await AccountModel.findOne({ subAccounts: account._id }, { features: 1 });

      if (agency) {
        features = _merge([], features, agency.toObject().features || []);
      }
    }
  }

  return features;
};

const findCustomTheme = (theme, customThemes) => {
  const themeIndex = customThemes.findIndex(
    ({ name: customThemeName }) => customThemeName === theme.name,
  );
  if (themeIndex < 0) return theme;
  const [customTheme] = customThemes.splice(themeIndex, 1);
  if (!customTheme.templateCount) return null;
  const merged = _merge({}, theme, customTheme);
  return merged;
};

const themeMapperBuilder = (customThemes) => (theme) => {
  const customTheme = findCustomTheme(theme, customThemes);
  if (!customTheme) return null;
  const {
    _id,
    name,
    textColor,
    lead,
    featured,
    thumbnail,
    thumbnailV2 = null,
    mainTemplate,
    themeKit,
    color,
    multicolor,
    logo,
  } = customTheme;
  return {
    _id,
    name,
    textColor,
    lead,
    featured,
    thumbnail,
    thumbnailV2,
    multicolor: !!themeKit || multicolor,
    mainTemplate,
    color: themeKit?.colors.mainColor || color,
    themeKit,
    logo,
  };
};

const mergeThemes = async (oldThemes, customThemes) => {
  if (!customThemes.length) return oldThemes;
  const customThemesTemplateCounts = await Promise.all(
    customThemes.map(({ name }) =>
      Template.model.countDocuments({ 'template.themeKit.name': name, status: 'published' }),
    ),
  );
  const customThemesWithTemplateCounts = customThemes.map((theme, index) => ({
    ...theme,
    templateCount: customThemesTemplateCounts[index] || 0,
  }));
  const themeMapper = themeMapperBuilder(customThemesWithTemplateCounts);

  return oldThemes.map(themeMapper).filter((v) => !!v);
};

const getThemes = async () => {
  const [{ value: oldThemes }] = await Promise.all([
    configModel.findOne({ key: 'template_themes' }),
  ]);
  let themes = oldThemes;

  const customThemes = await CustomThemeModel.find(
    {
      sourceTheme: { $exists: false },
      sourceThemeName: { $exists: false },
    },
    {
      _id: 1,
      name: 1,
      textColor: 1,
      lead: 1,
      featured: 1,
      thumbnail: 1,
      mainTemplate: 1,
      'themeKit.colors.mainColor': 1,
      'themeKit.fonts': 1,
      'themeKit.rounding': 1,
    },
  ).lean();
  themes = await mergeThemes(oldThemes, customThemes);

  return themes;
};

const getSortedSeasons = async () => {
  const cacheKey = 'templates:sortedSeasons';
  const cached = await redisClient.get(cacheKey);
  if (cached) return JSON.parse(cached);

  const { value: seasonsByPeriod } = await configModel.findOne(
    { key: 'template_categories_by_period' },
    { value: 1 },
  );
  const filteredSeasons = seasonsByPeriod.filter((v) => !['corona', 'kata'].includes(v.key));
  filteredSeasons.sort((aCategory, bCategory) => {
    const aStart = aCategory.period.start;
    const bStart = bCategory.period.start;
    return aStart.getTime() - bStart.getTime();
  });

  const now = new Date();
  now.setFullYear(2000);
  const mostRecentIndex = filteredSeasons.findIndex(
    ({ period }) => now.getTime() < period.end.getTime(),
  );

  const notRecentSeasons = filteredSeasons.splice(0, mostRecentIndex);
  filteredSeasons.push(...notRecentSeasons);

  const seasons = filteredSeasons.map((v) => v.key);

  redisClient.setex(cacheKey, JSON.stringify(seasons), CACHE_TTL_1_DAY);
  return seasons;
};

const getBaseCustomThemeNames = async () => {
  const cacheKey = 'templates:custom-theme-names-240423';
  const cached = await redisClient.get(cacheKey);

  if (cached) return JSON.parse(cached);

  const baseThemes = await CustomThemeModel.find(
    { sourceTheme: { $exists: false }, databaseId: 44 },
    { name: 1 },
  );
  const deduplicatedBaseThemeNamesSet = new Set(baseThemes?.map?.(({ name }) => name) ?? []);
  const baseThemeNameList = Array.from(deduplicatedBaseThemeNamesSet.values());

  redisClient.setex(cacheKey, JSON.stringify(baseThemeNameList), CACHE_TTL_1_HOUR);

  return baseThemeNameList;
};

const buildCategoryFilter = async ({ criteria, category, search }) => {
  // Dont check length custom templates listing depends on it! (it has to be true)
  if (category && !search) {
    const categoryKey = category?.length ? 'categories' : 'categories.0';
    const categoryValue = category?.length
      ? { $in: Array.isArray(category) ? category : [category] }
      : { $exists: false };
    criteria[categoryKey] = categoryValue;
    const baseCustomThemes = await getBaseCustomThemeNames();
    criteria.theme = { $nin: baseCustomThemes };
  }
};

const buildThemeKitCriteria = (themeKit) => {
  if (!themeKit) return undefined;
  const value = Array.isArray(themeKit) ? themeKit : [themeKit];
  return value.map((id) => ObjectId(id));
};

const buildThemeKitFilter = ({ criteria, themeKit }) => {
  const themeKitFilter = buildThemeKitCriteria(themeKit);
  if (themeKitFilter?.length) {
    criteria['template.themeKit.id'] = { $in: themeKitFilter };
    criteria.universal = true;
  }
};

const buildTypeFilter = ({ criteria, types }) => {
  const realTypes = types.filter((t) => !['gamification', 'survey'].includes(t));

  if (realTypes.includes('interstitial')) {
    realTypes.push(FILTER_KEY_FULLSCREEN);
  }

  if (realTypes?.length) {
    criteria.type = { $in: realTypes };
  }
  const isGamificationFiltered = types.includes('gamification');
  const isSurveyFiltered = types.includes('survey');
  if (isGamificationFiltered && isSurveyFiltered) {
    criteria.$or = [{ gamification: true }, { survey: true }];
  } else if (isGamificationFiltered) {
    criteria.gamification = true;
  } else if (isSurveyFiltered) {
    criteria.survey = true;
  }
};

const querySortedSeasonalBanners = async (locale = 'hu') => {
  const seasonTemplates = await configModel.aggregate([
    { $match: { key: 'template_categories_by_period' } },
    { $unwind: '$value' },
    { $match: { 'value.locale': locale } },
    { $sort: { 'value.period.start': 1 } },
    {
      $lookup: {
        from: 'master_config',
        let: { seasonKey: '$value.key' },
        pipeline: [
          { $match: { key: 'template_categories_banners' } },
          { $unwind: '$value' },
          { $match: { $expr: { $eq: ['$value.name', '$$seasonKey'] } } },
          { $project: { _id: 0, img: '$value.img' } },
        ],
        as: 'banners',
      },
    },
    { $unwind: '$banners' },
    {
      $project: {
        _id: 0,
        key: '$value.key',
        period: '$value.period',
        locale: '$value.locale',
        img: '$banners.img',
      },
    },
  ]);

  if (!seasonTemplates.length) return [];

  const actualYear = new Date().getFullYear();

  // Actualize dates and sort by start date
  return seasonTemplates.map((template) => {
    const result = {
      ...template,
      period: {
        start: updateYear(template.period.start, actualYear),
        end: updateYear(template.period.end, actualYear),
      },
    };
    return result;
  });
};

const updateYear = (dateNum, year) => {
  const date = new Date(dateNum);
  date.setFullYear(year);
  return date.getTime();
};

const findActiveOrClosestTemplate = async (locale, now) => {
  const banners = await querySortedSeasonalBanners(locale);
  if (!banners.length) return [];

  // Filter out banners where now is between start and end, or in the future
  let activeBanners = banners.filter((banner) => {
    const start = banner.period.start;
    const end = banner.period.end;
    return (start <= now && end >= now) || (start >= now && end >= now);
  });

  // If not in any interval, find the closest
  const upcomings = activeBanners
    .map((banner, index) => {
      const res = {
        diff: Math.abs(new Date(banner.period.start) - now),
        index,
      };
      return res;
    })
    .sort((a, b) => a.diff - b.diff);

  let result;

  if (upcomings.length) {
    result = {
      ...activeBanners[upcomings[0].index],
      period: {
        start: new Date(activeBanners[upcomings[0].index].period.start).toISOString(),
        end: new Date(activeBanners[upcomings[0].index].period.end).toISOString(),
      },
    };
  } else {
    // if no active banner, means new years eve is the next one
    const nextYear = new Date().getFullYear() + 1;

    const newYearBanner = banners.find((banner) => banner.key === 'new_year');

    if (!newYearBanner) {
      return undefined;
    }

    result = {
      ...newYearBanner,
      period: {
        start: new Date(updateYear(banners[0].period.start, nextYear)).toISOString(),
        end: new Date(updateYear(banners[0].period.end, nextYear)).toISOString(),
      },
    };
  }
  return result;
};

module.exports = {
  mapUseCasesByID,
  getTemplateQuery,
  getAuthorAndSubAccount,
  TEMPLATE_STATUS,
  notEmptyArray,
  getFilteredTemplates,
  getNormalTemplateQuery,
  getNormalFilteredTemplates,
  getAccountFeatures,
  getThemes,
  mergeThemes,
  getSortedSeasons,
  buildCategoryFilter,
  buildThemeKitFilter,
  buildTypeFilter,
  resolveTemplateByType,
  getBaseCustomThemeNames,
  findActiveOrClosestTemplate,
  FILTER_KEY_FULLSCREEN,
};
