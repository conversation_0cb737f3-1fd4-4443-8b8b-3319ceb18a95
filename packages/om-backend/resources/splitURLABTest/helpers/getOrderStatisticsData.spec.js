const { mock } = require('@om/queries/src/testUtils');
const { materializedViewSchema, ordersSchema } = require('@om/queries');
const { eventFactory, orderFactory } = require('../../goal/helpers/testFixtures');

const { getOrderStatisticsData } = require('./getOrderStatisticsData');

jest.mock('../../goal/helpers/util', () => {
  return {
    BQ_PROJECT_ID: 'test_bq_project',
  };
});

const run = ({ testData = [], startDate, endDate, useNonstandardOrders }) => {
  const events = testData.flatMap((deviceData) => deviceData.events);
  const orders = testData.flatMap((deviceData) => deviceData.orders);
  const materializedViewName = mock(events, materializedViewSchema);
  const ordersTableName = mock(orders, ordersSchema);

  return getOrderStatisticsData({
    materializedViewName,
    ordersTableName,
    accountId: '123456',
    providerServiceId: 'lions-not-sheep.myshopify.com',
    useNonstandardOrders,
    startDate,
    endDate,
  });
};

describe('Split URL A/B test - getOrderStatisticsData tests', () => {
  test('should return correct order count and total revenue', async () => {
    const testData = [
      // device 1 with split url impression and order
      {
        events: [
          eventFactory({
            timestamp: '2023-04-15T00:00:00.000Z',
            field: 'splitURLTest_variantId',
            value: 'variant1234',
            deviceId: 'd1',
          }),
        ],
        orders: [
          orderFactory({
            timestamp: '2023-04-15T00:00:01.000Z',
            currency: 'USD',
            total: '10',
            deviceId: 'd1',
          }),
        ],
      },

      // device 2 with split url impression and orders
      {
        events: [
          eventFactory({
            timestamp: '2023-04-15T00:01:00.000Z',
            field: 'splitURLTest_variantId',
            value: 'variant1234',
            deviceId: 'd2',
          }),
        ],
        orders: [
          orderFactory({
            timestamp: '2023-04-15T00:01:01.000Z',
            currency: 'USD',
            total: '10',
            deviceId: 'd2',
          }),
          orderFactory({
            timestamp: '2023-04-16T00:01:01.000Z',
            currency: 'USD',
            total: '10',
            deviceId: 'd2',
          }),
        ],
      },

      // device 3 without split url impression, only not related eoi
      {
        events: [
          eventFactory({
            timestamp: '2023-04-15T00:01:00.000Z',
            field: 'eoi_variantId',
            value: 'variant1234',
            deviceId: 'd3',
          }),
        ],
        orders: [
          orderFactory({
            timestamp: '2023-04-15T00:01:01.000Z',
            currency: 'USD',
            total: '99',
            deviceId: 'd3',
          }),
        ],
      },
    ];

    const result = await run({
      testData,
      useNonstandardOrders: false,
      startDate: '2023-04-01T00:00:00.000Z',
      endDate: '2023-05-01T00:00:00.000Z',
    });

    expect(result).toEqual([
      expect.objectContaining({
        variantId: 'variant1234',
        totalRevenue: 30,
        orderCount: 3,
      }),
    ]);
  });

  test('should handle the date interval', async () => {
    const testData = [
      {
        // inside the interval
        events: [
          eventFactory({
            timestamp: '2023-04-01T00:00:00.000Z',
            field: 'splitURLTest_variantId',
            value: 'variant1234',
            deviceId: 'd1',
          }),
        ],
        orders: [
          orderFactory({
            timestamp: '2023-04-01T00:00:01.000Z',
            currency: 'USD',
            total: '10',
            deviceId: 'd1',
          }),
        ],
      },
      {
        // outside the interval, before
        events: [
          eventFactory({
            timestamp: '2023-03-01T00:00:00.000Z',
            field: 'splitURLTest_variantId',
            value: 'variant1234',
            deviceId: 'd1',
          }),
        ],
        orders: [
          orderFactory({
            timestamp: '2023-03-02T00:00:00.000Z',
            currency: 'USD',
            total: '10',
            deviceId: 'd1',
          }),
        ],
      },
      {
        // outside the interval, after
        events: [
          eventFactory({
            timestamp: '2023-05-01T00:00:01.000Z',
            field: 'splitURLTest_variantId',
            value: 'variant1234',
            deviceId: 'd1',
          }),
        ],
        orders: [
          orderFactory({
            timestamp: '2023-03-02T00:00:00.000Z',
            currency: 'USD',
            total: '10',
            deviceId: 'd1',
          }),
        ],
      },
    ];

    const result = await run({
      testData,
      useNonstandardOrders: false,
      startDate: '2023-04-01T00:00:00.000Z',
      endDate: '2023-05-01T00:00:00.000Z',
    });

    expect(result).toEqual([
      expect.objectContaining({
        variantId: 'variant1234',
        totalRevenue: 10,
        orderCount: 1,
      }),
    ]);
  });

  test('should handle aggregation by variant', async () => {
    const testData = [
      // variant 1, device 1
      {
        events: [
          eventFactory({
            timestamp: '2023-04-15T00:00:00.000Z',
            field: 'splitURLTest_variantId',
            value: 'variant1',
            deviceId: 'd1',
          }),
        ],
        orders: [
          orderFactory({
            timestamp: '2023-04-15T00:00:01.000Z',
            currency: 'USD',
            total: '11',
            deviceId: 'd1',
          }),
        ],
      },

      // variant 2-3, device 2 (one order counts for two variants)
      {
        events: [
          eventFactory({
            timestamp: '2023-04-15T00:01:00.000Z',
            field: 'splitURLTest_variantId',
            value: 'variant2',
            deviceId: 'd2',
          }),
          eventFactory({
            timestamp: '2023-04-15T00:01:01.000Z',
            field: 'splitURLTest_variantId',
            value: 'variant3',
            deviceId: 'd2',
          }),
        ],
        orders: [
          orderFactory({
            timestamp: '2023-04-15T00:10:00.000Z',
            currency: 'USD',
            total: '12',
            deviceId: 'd2',
          }),
        ],
      },
    ];

    const result = await run({
      testData,
      useNonstandardOrders: false,
      startDate: '2023-04-01T00:00:00.000Z',
      endDate: '2023-05-01T00:00:00.000Z',
    });

    expect(result.length).toEqual(3);
    expect(result).toEqual([
      expect.objectContaining({
        variantId: 'variant1',
        totalRevenue: 11,
        orderCount: 1,
      }),
      expect.objectContaining({
        variantId: 'variant2',
        totalRevenue: 12,
        orderCount: 1,
      }),
      expect.objectContaining({
        variantId: 'variant3',
        totalRevenue: 12,
        orderCount: 1,
      }),
    ]);
  });

  test('should handle aggregation by currency', async () => {
    const testData = [
      // currency 1 (USD), device 1
      {
        events: [
          eventFactory({
            timestamp: '2023-04-15T00:00:00.000Z',
            field: 'splitURLTest_variantId',
            value: 'variant1234',
            deviceId: 'd1',
          }),
        ],
        orders: [
          orderFactory({
            timestamp: '2023-04-15T00:00:01.000Z',
            currency: 'USD',
            total: '10',
            deviceId: 'd1',
          }),
        ],
      },

      // currency 1 (USD), device 2
      {
        events: [
          eventFactory({
            timestamp: '2023-04-15T00:01:00.000Z',
            field: 'splitURLTest_variantId',
            value: 'variant1234',
            deviceId: 'd2',
          }),
        ],
        orders: [
          orderFactory({
            timestamp: '2023-04-15T00:01:01.000Z',
            currency: 'USD',
            total: '10',
            deviceId: 'd2',
          }),
        ],
      },

      // currency 2 (HUF), device 3
      {
        events: [
          eventFactory({
            timestamp: '2023-04-15T00:02:00.000Z',
            field: 'splitURLTest_variantId',
            value: 'variant1234',
            deviceId: 'd3',
          }),
        ],
        orders: [
          orderFactory({
            timestamp: '2023-04-15T00:02:01.000Z',
            currency: 'HUF',
            total: '15',
            deviceId: 'd3',
          }),
        ],
      },
    ];

    const result = await run({
      testData,
      useNonstandardOrders: false,
      startDate: '2023-04-01T00:00:00.000Z',
      endDate: '2023-05-01T00:00:00.000Z',
    });

    expect(result.length).toEqual(2);
    expect(result).toEqual([
      expect.objectContaining({
        variantId: 'variant1234',
        currency: 'USD',
        totalRevenue: 20,
        orderCount: 2,
      }),
      expect.objectContaining({
        variantId: 'variant1234',
        currency: 'HUF',
        totalRevenue: 15,
        orderCount: 1,
      }),
    ]);
  });

  test('should handle ga4-non-standard-orders mode', async () => {
    const testData = [
      {
        events: [
          eventFactory({
            timestamp: '2023-04-15T00:00:00.000Z',
            field: 'splitURLTest_variantId',
            value: 'variant1234',
          }),
        ],
        orders: [
          orderFactory({
            timestamp: '2023-04-15T00:00:01.000Z',
            currency: null,
            total: null,
            source: 'ga4_nonstandard',
          }),
        ],
      },
    ];

    const result = await run({
      testData,
      useNonstandardOrders: true,
      startDate: '2023-04-01T00:00:00.000Z',
      endDate: '2023-05-01T00:00:00.000Z',
    });

    expect(result).toEqual([
      expect.objectContaining({
        variantId: 'variant1234',
        currency: null,
        totalRevenue: null,
        orderCount: 1,
      }),
    ]);
  });

  test('should handle standard-orders mode', async () => {
    const testData = [
      {
        // device 1 with ga4-non-standard-order (should not be included)
        events: [
          eventFactory({
            timestamp: '2023-04-15T00:01:00.000Z',
            field: 'splitURLTest_variantId',
            value: 'variant1234',
            deviceId: 'd1',
          }),
        ],
        orders: [
          orderFactory({
            timestamp: '2023-04-15T00:01:01.000Z',
            currency: null,
            total: null,
            source: 'ga4_nonstandard',
            deviceId: 'd1',
          }),
        ],
      },

      {
        // device 2 with ga4-standard-order
        events: [
          eventFactory({
            timestamp: '2023-04-15T00:02:00.000Z',
            field: 'splitURLTest_variantId',
            value: 'variant1234',
            deviceId: 'd1',
          }),
        ],
        orders: [
          orderFactory({
            timestamp: '2023-04-15T00:02:01.000Z',
            currency: 'USD',
            total: 10,
            source: 'ga4',
            deviceId: 'd1',
          }),
        ],
      },

      {
        // device 2 with webhook-order
        events: [
          eventFactory({
            timestamp: '2023-04-15T00:03:00.000Z',
            field: 'splitURLTest_variantId',
            value: 'variant1234',
            deviceId: 'd3',
          }),
        ],
        orders: [
          orderFactory({
            timestamp: '2023-04-15T00:03:01.000Z',
            currency: 'USD',
            total: 10,
            source: 'webhook',
            deviceId: 'd3',
          }),
        ],
      },
    ];

    const result = await run({
      testData,
      useNonstandardOrders: false,
      startDate: '2023-04-01T00:00:00.000Z',
      endDate: '2023-05-01T00:00:00.000Z',
    });

    expect(result).toEqual([
      expect.objectContaining({
        variantId: 'variant1234',
        totalRevenue: 20,
        orderCount: 2,
      }),
    ]);
  });
});
