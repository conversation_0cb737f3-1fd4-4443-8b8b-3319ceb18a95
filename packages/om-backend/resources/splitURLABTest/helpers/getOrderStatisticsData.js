const { BQ_PROJECT_ID, getMaterializedViewName } = require('../../goal/helpers/util');
const { BigQueryClient } = require('../../../services/bigQueryAdapter');
const log = require('../../../logger');

const _getTableName = (tableName) => {
  return tableName.startsWith('(') ? tableName : `\`${tableName}\``;
};

const getOrderStatisticsData = async ({
  accountId,
  providerServiceId,
  useNonstandardOrders,
  startDate,
  endDate,
  materializedViewName: _materializedViewName = null,
  ordersTableName: _ordersTableName = null,
}) => {
  const materializedViewName =
    _materializedViewName || (await getMaterializedViewName({ providerServiceId, accountId }));
  const goalMaterialisedView = _getTableName(materializedViewName);

  const ordersTableName = _ordersTableName || `${BQ_PROJECT_ID}.orders_optimonk.unified_orders`;
  const orderTable = _getTableName(ordersTableName);

  const impressionEventName = 'splitUrlImpression';
  const groupBy = 'splitURLTestVariantId';

  const query = `WITH
 orders AS (
    SELECT
      timestamp,
      deviceId,
      total,
      currency
    FROM ${orderTable}
    WHERE timestamp BETWEEN TIMESTAMP('${startDate}') AND TIMESTAMP('${endDate}')
    AND accountId = '${accountId}'
    AND REGEXP_REPLACE(providerServiceId, r'^(www\.|m\.)', '') = '${providerServiceId}'
    ${
      useNonstandardOrders
        ? "AND source = 'ga4_nonstandard'"
        : "AND (source != 'ga4_nonstandard' OR source IS NULL)"
    }
    GROUP BY timestamp, deviceId, orderId, total, currency
 ),
 other_events AS (
   SELECT
     timestamp,
     field,
     deviceId,
     value
   FROM ${goalMaterialisedView}
   WHERE NOT REGEXP_CONTAINS(field,r'^eoo_')
   AND timestamp BETWEEN TIMESTAMP_SUB(TIMESTAMP('${startDate}'), INTERVAL 5 DAY) AND TIMESTAMP('${endDate}')
 ),
 ${impressionEventName} AS (
   SELECT
     timestamp,
     deviceId,
     other_events.value as ${groupBy}
   FROM
     other_events
   WHERE other_events.field = 'splitURLTest_variantId'
 ),
 splitUrlTestOrders AS (
   SELECT DISTINCT
     orders.deviceId,
     ${impressionEventName}.${groupBy} as ${groupBy},
     orders.timestamp as goalTS,
     orders.total,
     orders.currency
   FROM
     orders
     INNER JOIN ${impressionEventName} ON (orders.deviceId = ${impressionEventName}.deviceId AND ${impressionEventName}.timestamp
       BETWEEN TIMESTAMP_SUB(orders.timestamp, INTERVAL 5 DAY) AND orders.timestamp)
 )

  SELECT
  ${groupBy} as variantId,
  currency,
  SUM(SAFE_CAST(total as FLOAT64)) as totalRevenue,
  COUNT(*) as orderCount
  FROM splitUrlTestOrders
  GROUP BY variantId, currency
  `;

  const bigQueryClient = new BigQueryClient();
  const queryStart = performance.now();
  const queryResult = await bigQueryClient.runQuery(query);
  const queryEnd = performance.now();

  log.info({
    accountId,
    took: queryEnd - queryStart,
    message: `Split URL A/B test - getOrderStatisticsData live query ${accountId}`,
  });

  return queryResult;
};

module.exports = { getOrderStatisticsData };
