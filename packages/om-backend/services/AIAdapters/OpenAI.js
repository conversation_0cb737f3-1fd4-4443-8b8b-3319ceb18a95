const axios = require('axios');
const logger = require('../../logger').child({ service: 'openai-adapter' });

const API_KEY = process.env.open_ai_api_key;

class OpenAIAdapter {
  static buildPayload(prompt, options) {
    let payload = {
      model: 'gpt-4',
      messages: [{ role: 'user', content: prompt }],
      temperature: 1,
      top_p: 1,
      n: 1,
      stream: false,
    };

    if (options) {
      payload = { ...payload, ...options };
    }

    return payload;
  }

  static getAxiosInstance() {
    if (!API_KEY) throw Error('No API key');

    return axios.create({
      baseURL: 'https://api.openai.com/',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${API_KEY}`,
      },
    });
  }

  static async createCompletionRequests(prompts, options) {
    const requests = prompts.map((prompt) =>
      this.getAxiosInstance().post('/v1/chat/completions', this.buildPayload(prompt.text, options)),
    );

    const responses = await Promise.all(requests);

    return responses.map(({ data }, index) => ({
      ...data,
      content: data.choices[0].message.content,
      prompt: prompts[index],
    }));
  }

  static async getCompletions(prompts, options = {}) {
    try {
      return this.createCompletionRequests(prompts, options);
    } catch (error) {
      logger.error({
        message: 'OpenAI request error',
        prompts,
        options,
        errorMessage: error.message,
        stack: error.stack,
      });
      if (error.response.status === 429) {
        return this.createCompletionRequests(prompts, options);
      }
      throw error;
    }
  }
}

module.exports = OpenAIAdapter;
