const { RECOMMENDATION_STATES } = require('@om/integrations');
const RecommendationStateResolver = require('./RecommendationStateResolver');

describe('RecommendationStateResolver test', () => {
  test('No active campaign with same integration', () => {
    const integrationsCount = 0;
    const campaignsWithIntegration = 0;
    const integrationType = 'nonKlaviyo';
    const resolver = new RecommendationStateResolver(
      integrationType,
      integrationsCount,
      campaignsWithIntegration,
    );

    expect(resolver.getRecommendationState()).toEqual(RECOMMENDATION_STATES.NONE);
  });

  test('No active campaign with klaviyo integration', () => {
    const integrationsCount = 0;
    const campaignsWithIntegration = 0;
    const integrationType = 'klaviyo';
    const resolver = new RecommendationStateResolver(
      integrationType,
      integrationsCount,
      campaignsWithIntegration,
    );

    expect(resolver.getRecommendationState()).toEqual(RECOMMENDATION_STATES.NONE);
  });

  test('No active campaign with klaviyo integration, klaviyo detected on domain', () => {
    const integrationsCount = 0;
    const campaignsWithIntegration = 0;
    const integrationType = 'klaviyo';
    const resolver = new RecommendationStateResolver(
      integrationType,
      integrationsCount,
      campaignsWithIntegration,
      true,
    );

    expect(resolver.getRecommendationState()).toEqual(RECOMMENDATION_STATES.ADD);
  });

  test('Has global integration with klaviyo integration, but klaviyo not detected on domain', () => {
    const integrationsCount = 1;
    const campaignsWithIntegration = 0;
    const integrationType = 'klaviyo';
    const resolver = new RecommendationStateResolver(
      integrationType,
      integrationsCount,
      campaignsWithIntegration,
    );

    expect(resolver.getRecommendationState()).toEqual(RECOMMENDATION_STATES.NONE);
  });

  test('Has 1 global integration with non-klaviyo integration', () => {
    const integrationsCount = 1;
    const campaignsWithIntegration = 1;
    const integrationType = 'nonKlaviyo';
    const resolver = new RecommendationStateResolver(
      integrationType,
      integrationsCount,
      campaignsWithIntegration,
    );

    expect(resolver.getRecommendationState()).toEqual(RECOMMENDATION_STATES.FINISH);
  });

  test('Has 1 global integration with non-klaviyo integration in multiple campaigns', () => {
    const integrationsCount = 1;
    const campaignsWithIntegration = 4;
    const integrationType = 'nonKlaviyo';
    const resolver = new RecommendationStateResolver(
      integrationType,
      integrationsCount,
      campaignsWithIntegration,
    );

    expect(resolver.getRecommendationState()).toEqual(RECOMMENDATION_STATES.FINISH);
  });

  test('Has multiple global integration with non-klaviyo integration', () => {
    const integrationsCount = 3;
    const campaignsWithIntegration = 4;
    const integrationType = 'nonKlaviyo';
    const resolver = new RecommendationStateResolver(
      integrationType,
      integrationsCount,
      campaignsWithIntegration,
    );

    expect(resolver.getRecommendationState()).toEqual(RECOMMENDATION_STATES.SELECT);
  });
});
