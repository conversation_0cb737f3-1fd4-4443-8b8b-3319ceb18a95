const { RECOMMENDATION_STATES } = require('@om/integrations');

class RecommendationStateResolver {
  constructor(
    integrationType,
    globalIntegrationCount,
    integrationUsageInCampaigns,
    isKlaviyoDetected,
  ) {
    this.integrationType = integrationType;
    this.globalIntegrationCount = globalIntegrationCount;
    this.integrationUsageInCampaigns = integrationUsageInCampaigns;
    this.isKlaviyoDetected = isKlaviyoDetected;
  }

  getRecommendationState() {
    let state = RECOMMENDATION_STATES.NONE;
    const integrationInCampaigns = this.integrationUsageInCampaigns > 0;

    if (integrationInCampaigns) {
      state = this.usedInCampaigns();
    } else {
      state = this.notUsedInCampaigns();
    }
    return state;
  }

  usedInCampaigns() {
    return this.globalIntegrationCount > 1
      ? RECOMMENDATION_STATES.SELECT
      : RECOMMENDATION_STATES.FINISH;
  }

  notUsedInCampaigns() {
    return this.integrationType === 'klaviyo' && this.isKlaviyoDetected
      ? RECOMMENDATION_STATES.ADD
      : RECOMMENDATION_STATES.NONE;
  }
}

module.exports = RecommendationStateResolver;
