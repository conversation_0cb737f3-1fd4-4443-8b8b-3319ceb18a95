const axios = require('axios');
const log = require('../../logger').child({ integration: 'klaviyo' });

const API_VERSION = '2024-06-15';

class KlaviyoAdapter {
  constructor({ apiKey }) {
    this.api = axios.create({
      baseURL: 'https://a.klaviyo.com/api',
      headers: {
        Authorization: `Klaviyo-API-Key ${apiKey}`,
        revision: API_VERSION,
      },
    });

    this.lists = [];
    this.segments = [];
  }

  async ping() {
    try {
      const response = await this.api.get('/lists');
      if (response.status === 200) {
        this.lists = this._formatList(response.data);
        return true;
      }
    } catch (error) {
      const errorMessage =
        error.response && error.response.data ? error.response.data : error.message;
      log.error(error, 'error while pinging klaviyo integration:', errorMessage);
      return false;
    }
  }

  async getData() {
    try {
      await this.getLists();

      return { success: true, lists: this.lists };
    } catch (e) {
      const error = e.response?.data?.message;
      return { success: false, error };
    }
  }

  async getSegmentsAndList() {
    await Promise.all([this.getSegments(), this.getLists()]);

    return [...this.lists, ...this.segments];
  }

  async getSegments() {
    this.segments = [];
    let nextPage;
    do {
      const { data: resp } = await this.api.get(nextPage ?? '/segments');
      nextPage = resp.links.next;

      this.segments.push(...this._formatList(resp));
    } while (nextPage);

    return this.segments;
  }

  async getLists() {
    this.lists = [];
    let nextPage;
    do {
      const { data: resp } = await this.api.get(nextPage ?? '/lists?sort=name');
      nextPage = resp.links.next;

      this.lists.push(...this._formatList(resp));
    } while (nextPage);

    return this.lists;
  }

  _formatList({ data }) {
    return data.map((item) => {
      const { id, attributes, type: listType } = item;
      return {
        id,
        name: attributes.name,
        listType,
      };
    });
  }
}

module.exports = KlaviyoAdapter;
