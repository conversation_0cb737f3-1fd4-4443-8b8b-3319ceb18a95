// dotmailer
// email: <EMAIL>
// password: test1test
// baseurl valszeg: https://r1-api.dotmailer.com/v2/account-info
const axios = require('axios');
const log = require('../../logger').child({ integration: 'dotmailer' });

class DotmailerAdapter {
  constructor({ username, password }) {
    this.api = axios.create({
      baseURL: 'https://r1-api.dotdigital.com/v2',
      auth: {
        username,
        password,
      },
    });
    this.isURLCorrected = false;
  }

  async setCorrectEndPoint() {
    if (this.isURLCorrected) return;

    const response = await this.api.get('/account-info');
    const accountProperties = response.data.properties;
    for (let i = accountProperties.length - 1; i >= 0; i--) {
      // usually it is the last property so we'll start there
      if (accountProperties[i].name === 'ApiEndpoint') {
        this.api.defaults.baseURL = `${accountProperties[i].value}/v2`;
        this.isURLCorrected = true;
        break;
      }
    }
  }

  async ping() {
    try {
      await this.setCorrectEndPoint();
      const response = await this.api.get('/account-info');
      if (response.status === 200) {
        return true;
      }
    } catch (error) {
      const errorMessage =
        error.response && error.response.data ? error.response.data : error.message;
      log.error(error, 'error while pinging dotmailer:', errorMessage);
      return false;
    }
  }

  // https://(api|r3-api|r1-api).dotmailer.com/v2/address-books/
  async getAddressBooks() {
    const { data } = await this.api.get('/address-books');
    return data.filter((a) => a.name !== 'Test');
  }

  async getDataFields() {
    const { data } = await this.api.get('/data-fields');
    return data.map((f) => {
      return { id: f.name, name: f.name };
    });
  }

  async getData() {
    const ret = { addressbooks: [], fields: [] };

    await this.setCorrectEndPoint();
    ret.addressbooks = await this.getAddressBooks();
    ret.fields = [{ id: 'EMAIL', name: 'EMAIL' }, ...(await this.getDataFields())];

    ret.success = true;

    return ret;
  }
}

// r1-api.dotmailer.com

// ;(async () => {
//   const dotmailerAdapter = new DotmailerAdapter({username: '<EMAIL>', password: 'test1234'})
//   let canConnect = await dotmailerAdapter.ping()
//   if (canConnect) {
//     let addressBookResult = await dotmailerAdapter.getAddressBooks()
//     console.log(addressBookResult)
//     console.log('---------------------------------------')
//     console.log('---------------------------------------')
//     console.log('---------------------------------------')
//     let dataFieldsResult = await dotmailerAdapter.getDataFields()
//     console.log(dataFieldsResult)
//   }
// })()

module.exports = DotmailerAdapter;
