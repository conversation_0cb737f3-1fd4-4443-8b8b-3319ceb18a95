const moment = require('moment');
const { model: userAlertModel } = require('../../resources/userAlert/userAlert.model');
const {
  model: userAlertEmailModel,
} = require('../../resources/userAlertEmail/userAlertEmail.model');
const { ALERT_TYPES } = require('./constants');
const { getAlertAggregationKey, getCaseIdentifier } = require('./helper');

const _mapProducts = (products) => {
  if (!products) return [];
  return products.map((product) => ({ id: product.id, title: product.title }));
};

const _mapLinks = (links) => {
  if (!links) return [];
  return links
    .filter((link) => ['Campaign', 'Variant'].includes(link.type))
    .map((link) => ({ id: link.id, type: link.type }));
};

const _removeLinkDuplications = (links) => {
  if (!links.length) return [];
  const seen = [];
  return links.filter((row) => {
    const identifier = `${row.id}-${row.type}`;
    if (seen.includes(identifier)) return false;
    seen.push(identifier);
    return true;
  });
};

const _removeProductDuplications = (products) => {
  if (!products.length) return [];
  const seen = [];
  return products.filter((row) => {
    const identifier = `${row.title}`;
    if (seen.includes(identifier)) return false;
    seen.push(identifier);
    return true;
  });
};

const _mergeAlerts = (alert1, alert2) => {
  const merged = alert1;
  merged.links = _removeLinkDuplications([...alert1.links, ..._mapLinks(alert2.links)]);
  merged.context.products = _removeProductDuplications([
    ...alert1.context.products,
    ..._mapProducts(alert2.context.products),
  ]);
  merged.userAlertIds.push(alert2._id);
  if (moment.utc(alert1.createdAt).isAfter(moment.utc(alert2.createdAt))) {
    merged.createdAt = alert2.createdAt;
  }
  return merged;
};

const _aggregateUserAlerts = (userAlertList) => {
  const aggregatedList = {};
  const isCampaignOutOfDiscountCodes = userAlertList.some(
    (alert) => alert.type === ALERT_TYPES.CampaignOutOfDiscountCodes,
  );
  userAlertList.forEach((userAlert) => {
    const databaseId = userAlert.databaseId;
    const type = userAlert.type;
    if (!type) return;
    if (isCampaignOutOfDiscountCodes && type === ALERT_TYPES.CampaignAlmostOutOfDiscountCodes)
      return;

    const key = getAlertAggregationKey(userAlert);
    if (aggregatedList[key]) {
      aggregatedList[key] = _mergeAlerts(aggregatedList[key], userAlert);
    } else {
      aggregatedList[key] = {
        databaseId,
        type,
        context: { products: _mapProducts(userAlert.context?.products) },
        links: _mapLinks(userAlert.links),
        createdAt: userAlert.createdAt,
        userAlertIds: [userAlert._id],
      };
    }
  });
  return Object.values(aggregatedList).map((userAlert) => ({
    ...userAlert,
    ...getCaseIdentifier(userAlert),
  }));
};

const getUserAlertList = async ({ filteredDatabaseId }) => {
  const filter = { type: { $in: Object.values(ALERT_TYPES) } };
  if (filteredDatabaseId) {
    filter.databaseId = filteredDatabaseId;
  }
  const list = await userAlertModel.find(filter).sort({ createdAt: 1 }).lean();
  const aggregatedList = _aggregateUserAlerts(list);
  const caseIdentifiers = aggregatedList.map((userAlert) => userAlert.caseIdentifier);

  const sentList = await userAlertEmailModel.find({ caseIdentifier: { $in: caseIdentifiers } });

  return aggregatedList.filter((userAlert) => {
    // - Check whether an email has already been sent for the same campaign, type, variants and products elements.
    //   => It will not be resent

    // - Determine whether it is a recurring problem (same data) by comparing the dates.
    //   If the previous alert has expired or been dismissed,
    //   the creation date of the current alert will be more recent then the date of sending email about the previous alert.
    //   => It will be sent again

    const found = sentList.find(
      (sent) =>
        sent.caseIdentifier === userAlert.caseIdentifier &&
        moment.utc(userAlert.createdAt).isBefore(moment.utc(sent.createdAt)),
    );
    return !found;
  });
};

module.exports = { getUserAlertList };
