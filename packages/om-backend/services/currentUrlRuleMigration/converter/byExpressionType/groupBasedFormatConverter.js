/*
Shopify / Shoprenter AND not embedded (v3) AND not DC
(~ Shopify / Shoprenter popups)

FROM
  { expressions: [[{ exp1 }, { exp2 }], [{ exp3 }]] }
TO
  {
    includeExpressions: [{ exp1 }, { exp2 }],
    excludeExpressions: [{ exp3 }],
  }
*/

const { NOT_OPERATORS, PAGE_TYPES } = require('../constants');
const {
  getOppositeOperator,
  removeDuplication,
  removePageTypeBasedDuplication,
} = require('../helper');

const convertWithGroupBaseFormat = (expressions) => {
  let includeExpressions = [];
  let excludeExpressions = [];

  let groupIndex = 0;
  for (const group of expressions) {
    for (const condition of group) {
      const pageType = condition.pageType ?? PAGE_TYPES.PAGE;
      if (NOT_OPERATORS.includes(condition.operator)) {
        excludeExpressions.push({
          pageType,
          ...condition,
          operator: getOppositeOperator(condition.operator),
          groupIndex,
        });
      } else {
        includeExpressions.push({
          pageType,
          ...condition,
          groupIndex,
        });
      }
    }
    groupIndex++;
  }

  includeExpressions = removePageTypeBasedDuplication(includeExpressions);
  excludeExpressions = removePageTypeBasedDuplication(excludeExpressions);

  includeExpressions.forEach((condition) => delete condition.groupIndex);
  excludeExpressions.forEach((condition) => delete condition.groupIndex);

  includeExpressions = removeDuplication(includeExpressions);
  excludeExpressions = removeDuplication(excludeExpressions);

  return {
    includeExpressions,
    excludeExpressions,
  };
};

module.exports = { convertWithGroupBaseFormat };
