const faker = require('faker');
const { model: TemplateModel } = require('../../resources/template/template.model');
const { getRandomIntInclusive } = require('../../util/random');
const log = require('../../logger').child({ script: __filename });

module.exports = async () => {
  const templates = [];

  const templateDatas = [
    {
      type: 'popup',
      data: [
        { name: 'NewYork', previewCount: 1 },
        { name: 'Milan', previewCount: 1 },
        { name: 'Monaco', previewCount: 3 },
        { name: 'Dallas', previewCount: 4 },
        { name: 'Madrid', previewCount: 2 },
        { name: 'Vegas', previewCount: 2 },
        { name: 'Roma', previewCount: 2 },
      ],
    },
    {
      type: 'nanobar',
      data: [
        { name: 'OptInBar', previewCount: 1 },
        { name: 'SocialBar', previewCount: 1 },
        { name: 'GDPRBar', previewCount: 1 },
      ],
    },
    {
      type: 'sidebar',
      data: [
        { name: 'Alps', previewCount: 2 },
        { name: '<PERSON>', previewCount: 2 },
        { name: '<PERSON>', previewCount: 2 },
        { name: 'Himalayas', previewCount: 2 },
      ],
    },
  ];

  for (const templateData of templateDatas) {
    for (const template of templateData.data) {
      const goals = [];
      for (let i = 1; i <= getRandomIntInclusive(3, 5); i++) {
        const random = faker.random.arrayElement(TemplateModel.schema.path('goals.0').enumValues);
        if (!goals.includes(random)) {
          goals.push(random);
        }
      }

      const previewUrls = [];
      for (let i = 1; i <= template.previewCount; i++) {
        if (templateData.type === 'nanobar') {
          previewUrls.push(
            `http://cdn-static.optimonk.com/bundles/wseintellipopup/Template/${template.name}/images/small.jpg`,
          );
        } else {
          previewUrls.push(
            `http://cdn-static.optimonk.com/bundles/wseintellipopup/Template/${template.name}/images/preview${i}.jpg`,
          );
        }
      }

      templates.push({
        name: template.name,
        template: {},
        type: templateData.type,
        previewUrls,
        goals,
      });
    }
  }

  await TemplateModel.insertMany(templates);

  log.info('----- All Templates generated -----');

  // process.exit(0)
};
