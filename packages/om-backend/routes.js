const multer = require('multer');

const storage = multer.memoryStorage();
const upload = multer({ storage });
const { graphiqlExpress } = require('apollo-server-express');
const rateLimit = require('express-rate-limit');
const RateLimitRedisStore = require('rate-limit-redis');
const { graphQLRouter } = require('./graphQLRouter');
const loginController = require('./controllers/login');
const campaignTemplateController = require('./controllers/campaign_template');
const campaignController = require('./controllers/campaign');
const authCampaignController = require('./controllers/authCampaign');
const usersController = require('./controllers/salesApi/users');
const shopSettingsController = require('./controllers/salesApi/shopSettings');
const migratorController = require('./controllers/migrator');
const oAuthTokenRefreshController = require('./controllers/oAuthTokenRefresh');

const salesUsername = process.env.sales_username;
const salesPassword = process.env.sales_password;
const fileUpload = require('./controllers/file_upload');
const statsController = require('./controllers/stats');
const emailTemplateController = require('./controllers/emailTemplate');
const cronController = require('./controllers/cron');
const exportController = require('./controllers/export');
const importController = require('./controllers/import');
const oAuthController = require('./controllers/integrationOAuth');
const ShopifyController = require('./controllers/app/shopify');
const HubSpotController = require('./controllers/app/hubspot');
const WordpressController = require('./controllers/app/wordpress');
const ScreenshotController = require('./controllers/screenshot');
const ZapierController = require('./controllers/zapier');
const CouponController = require('./controllers/frontend/coupon');
const unsubscribeController = require('./controllers/unsubscribe');
const ShoprenterController = require('./controllers/app/shoprenter');
const GamificationCouponControllerFactory = require('./controllers/frontend/gamificationCouponFactory');
const BigCommerceController = require('./controllers/app/bigcommerce');
const FacebookController = require('./controllers/app/facebook');
const trackingController = require('./controllers/tracking');
const backofficeController = require('./controllers/backoffice');
const notifyController = require('./controllers/frontend/notifyMe/notify');
const pncController = require('./controllers/pnc');
const { redis: client } = require('./services/ioRedisAdapter');
const templateBackupControllers = require('./controllers/templateBackup');
const {
  cookieAuth,
  getCampaignReport,
  getTopLevelDomainData,
  chanceToWin,
} = require('./controllers/reports');
const wizardController = require('./controllers/wizard');
const EmailController = require('./controllers/email');
const AmbassadorController = require('./controllers/ambassador');
const CustomerThemesController = require('./controllers/customThemes');
const YoutubeController = require('./controllers/youtube');
const SmartPersonalizationController = require('./controllers/smartPersonalization');
const SalesController = require('./controllers/sales');
const UserUseCasePreviewController = require('./controllers/userUseCasePreview');
const newsletterController = require('./controllers/newsletter/mailchimp');
const cacheController = require('./controllers/cache');
const PreviewController = require('./controllers/preview');

const apiLimiter = rateLimit({
  windowMs: 1000,
  max: 20,
  storage: new RateLimitRedisStore({ prefix: 'general_api', client }),
});
const graphQLLimiter = rateLimit({
  windowMs: 1000,
  max: 60,
  storage: new RateLimitRedisStore({ prefix: 'graphql_api', client }),
});
const cronLimiter = rateLimit({
  windowMs: 1000,
  max: 200,
  storage: new RateLimitRedisStore({ prefix: 'general_cron', client }),
});
const screenshotLimiter = rateLimit({
  windowMs: 1000,
  max: 10,
  storage: new RateLimitRedisStore({ prefix: 'general_screenshot', client }),
});
const newsletterLimiter = rateLimit({
  windowMs: 1000,
  max: 5,
  storage: new RateLimitRedisStore({ prefix: 'newsletter_preferences', client }),
});
const useCaseController = require('./controllers/useCase');
const configController = require('./controllers/config');

const salesAuthentication = (req, res, next) => {
  const auth = { login: salesUsername, password: salesPassword };

  const b64auth = (req.headers.authorization || '').split(' ')[1] || '';
  const [login, password] = Buffer.from(b64auth, 'base64').toString().split(':');

  if (!login || !password || login !== auth.login || password !== auth.password) {
    res.set('WWW-Authenticate', 'Basic');
    res.status(401).send('Authentication required.');
    return;
  }

  next();
};

module.exports = (app) => {
  app.use('/graphql', graphQLLimiter, graphQLRouter); // <--- WILL BE DEPRECATED AFTER PATH BASED BACKEND ROUTING
  app.use('/api/graphql', graphQLLimiter, graphQLRouter);
  app.use('/migrator', migratorController);
  app.use('/login', apiLimiter, loginController); // <--- WILL BE DEPRECATED AFTER PATH BASED BACKEND ROUTING
  app.use('/api/login', apiLimiter, loginController);
  if (process.env.ENABLE_GRAPIQL) app.use('/docs', graphiqlExpress({ endpointURL: '/graphql' }));
  app.use('/api/template', campaignTemplateController);
  app.use('/api/upload', upload.single('file'), fileUpload);
  app.use('/loaderio-79c680f01662be8e03d2541095484411', (req, res) => {
    res.send('loaderio-79c680f01662be8e03d2541095484411');
  });

  app.use('/cron', cronLimiter, cronController);
  app.use('/cache', cronLimiter, cacheController);
  app.use('/stats', statsController);
  app.use('/email-templates', emailTemplateController);
  app.use('/api/export', apiLimiter, exportController);
  app.use('/api/import', apiLimiter, importController);
  app.use('/api/use-case', useCaseController);
  app.use('/api/config', configController);
  app.use('/api/integration-oauth', oAuthController);
  app.use('/api/integration-refresh', oAuthTokenRefreshController);
  app.use('/api/screenshot', screenshotLimiter, ScreenshotController);
  app.use('/api/smart-personalizer', apiLimiter, SmartPersonalizationController);
  app.use('/app/shopify', ShopifyController);
  app.use('/app/zapier', ZapierController);
  app.use('/app/wordpress', WordpressController);
  app.use('/app/shoprenter', ShoprenterController);
  app.use('/app/bigcommerce', BigCommerceController);
  app.use('/app/facebook', FacebookController);
  app.use('/app/hubspot', HubSpotController);

  app.post('/api/coupon/:omid/lock', CouponController.lock);
  app.post('/api/coupon/:omid/unlock', CouponController.unlock);
  app.post('/api/coupon/:omid', CouponController.get);

  app.post('/api/scratch/:omid', GamificationCouponControllerFactory.create('scratchCard'));
  app.post('/api/pickapresent/:omid', GamificationCouponControllerFactory.create('pickAPresent'));

  app.get('/api/reports/campaigns', cookieAuth, getCampaignReport);
  app.get('/api/reports/get-top-level-domain-data', cookieAuth, getTopLevelDomainData);
  app.post('/api/reports/chance-to-win', cookieAuth, chanceToWin);
  app.use('/api/ambassador', cookieAuth, AmbassadorController);
  app.get('/api/youtube/video', cookieAuth, YoutubeController.getYoutubeData);

  app.use(
    '/api/:shop(shopify|magento|wordpress|shopware)Setting',
    salesAuthentication,
    shopSettingsController,
  ); // used by old sales uninstall webhook
  app.use('/api/users', salesAuthentication, usersController); // used by old sales uninstall webhook

  app.use('/unsubscribe', unsubscribeController);

  app.use('/api/backoffice', backofficeController);
  app.use('/', trackingController);
  app.use('/api/save/template/:databaseId/:campaignId', templateBackupControllers.save);
  app.use('/api/load/template/:databaseId/:campaignId', templateBackupControllers.load);
  app.use('/notify', notifyController);
  app.use('/api/web-selector', pncController);
  app.use('/api/wizard', wizardController);
  app.use('/api/email', EmailController);
  app.use('/api/custom-theme', CustomerThemesController);
  app.use('/api/campaign', campaignController);
  app.use('/api/auth-campaign', authCampaignController);

  app.use('/sales', SalesController);

  app.use('/template-previews', UserUseCasePreviewController);
  app.use('/preview', PreviewController);

  app.use('/api/newsletter/mailchimp/member-interests', newsletterLimiter, newsletterController);
};
